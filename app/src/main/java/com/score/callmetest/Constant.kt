package com.score.callmetest

object Constant {
    const val BASE_URL = "test-app.callmeso.com"
    const val BASE_IM_URL = "test-im.callmeso.com"
    const val BASE_LOG_URL = "test-log.callmeso.com"

    const val BASE_URL_RELEASE = "water.callmeso.com"
    const val BASE_IM_URL_RELEASE = "bargain.callmeso.com"
    const val BASE_LOG_URL_RELEASE = "noble.callmeso.com"

    const val USE_TRANSITION = false

    fun getBaseUrl(): String {
        return if (BuildConfig.SERVER_TYPE == 2) BASE_URL else BASE_URL_RELEASE
    }

    fun getImUrl(): String {
        return if (BuildConfig.SERVER_TYPE == 2) BASE_IM_URL else BASE_IM_URL_RELEASE
    }

    fun getLogUrl(): String {
        return if (BuildConfig.SERVER_TYPE == 2) BASE_LOG_URL else BASE_LOG_URL_RELEASE
    }

    fun getApplicationName() : String {
        return if (BuildConfig.SERVER_TYPE == 2)
            BuildConfig.APPLICATION_ID
        else BuildConfig.APPLICATION_ID.replace("test", "")
    }

    const val RELOGIN = "relogin"

    const val SUCCESS = "success"
    const val FAIL = "fail"

    const val ROBOt_ID = "Custom Service"

    // SharedPreferences
    const val KEY_IS_FIRST_LOGIN = "isFirstLogin"
    const val KEY_LAST_LOGIN_TYPE = "lastLoginType"
    const val KEY_ORDER_INFOS = "orderInfos"

    // Settings keys
    const val AUTO_TRANSLATE = "auto_translate"


    // Header keys
    const val HEADER_VER = "ver"
    const val HEADER_SEC_VER = "sec_ver"
    const val HEADER_PLATFORM_VER = "platform_ver"
    const val HEADER_DEVICE_ID = "device-id"
    const val HEADER_UTM_SOURCE = "utm-source"
    const val HEADER_MODEL = "model"
    const val HEADER_LANG = "lang"
    const val HEADER_SYS_LAN = "sys_lan"
    const val HEADER_IS_ANCHOR = "is_anchor"
    const val HEADER_PKG = "pkg"
    const val HEADER_PLATFORM = "platform"
    const val HEADER_AUTHORIZATION = "Authorization"
    const val HEADER_ATTRIBUTION_SDK = "attribution_sdk"
    const val HEADER_ATTRIBUTION_SDK_VER = "attribution_sdk_ver"
    const val HEADER_CAMPAIGN = "campaign"
    const val HEADER_CAMPAIGN_ID = "campaign_id"
    const val HEADER_AF_ADGROUP_ID = "af_adgroup_id"
    const val HEADER_AF_ADSET = "af_adset"
    const val HEADER_AF_ADSET_ID = "af_adset_id"
    const val HEADER_AF_STATUS = "af_status"
    const val HEADER_AF_AGENCY = "af_agency"
    const val HEADER_AF_CHANNEL = "af_channel"
    const val HEADER_DEVICE_LANG = "device_lang"
    const val HEADER_DEVICE_COUNTRY = "device_country"
    const val HEADER_TIME_ZONE = "time_zone"
    const val HEADER_AD_ID = "google_ad_id"
    const val HEADER_RC_TYPE = "rc_type"

    // Header values
    const val HEADER_PLATFORM_VALUE = "Android"
    const val HEADER_IS_ANCHOR_VALUE = "false"

    // Token key
    const val TOKEN_KEY = "token"

    const val ENCRYPT_KEY = "encrypt_key"

    // AppConfig相关常量
    const val APP_CONFIG_JSON = "app_config_json"
    const val APP_CONFIG_SAVE_TIME = "app_config_save_time"

    const val USER_ID = "user_id"

    const val EXTRA_CALL_STATE = "extra_call_state"
    const val AVATAR_URL = "avatar_url"
    const val NICKNAME = "nickname"
    const val AGE = "age"
    const val COUNTRY = "country"
    const val UNIT_PRICE = "unitPrice"
    const val FREE_TIP = "free_tip"
    const val IS_FREE = "is_free"
    const val CHANNEL_NAME = "channelName"
    const val FROM_USER_ID = "fromUserId"
    const val TO_USER_ID = "toUserId"
    const val MSG_CONTENT = "content"
    const val FREE_CALL_DURATION = "freeCallDuration"
    const val RTC_TOKEN = "rtc_token"

    const val BROADCASTER_MODEL = "broadcaster_model"
}

object CallStatus {
    const val ONLINE = "Online"
    const val IN_CALL = "InCall"
    const val OFFLINE = "Offline"
    const val BUSY = "Busy"
    const val AVAILABLE = "Available"
    const val UNKNOWN = "Unknown"

    /**
     *  str->status
     *  防止直接使用string不匹配status
     *
     * @param [status] 状态
     * @return [String]
     */
    fun valueOf(status: String): String {
        return when (status) {
            ONLINE -> ONLINE
            IN_CALL -> IN_CALL
            OFFLINE -> OFFLINE
            BUSY -> BUSY
            AVAILABLE -> AVAILABLE
            else -> UNKNOWN
        }
    }

    /**
     * 获取状态的显示文本（用于toast等UI显示）
     *
     * @param [status] 状态
     * @return [String] 状态的显示文本
     */
    fun getDisplayText(status: String): String {
        return when (status) {
            OFFLINE -> "offline"
            BUSY -> "busy"
            IN_CALL -> "incall"
            else -> status.lowercase()
        }
    }
}

/**
 * 通话历史记录状态
 */
object CallType {
    const val OUTGOING_CALL = "Outgoing call"    // 拨出且接通
    const val CANCELLED_CALL = "Cancelled call"   // 拨出并自己取消的通话
    const val UNANSWERED_CALL = "Unanswered call"  // 拨出自己没有取消但对方挂断、后台异常挂断或超时挂断
    const val INCOMING_CALL = "Incoming call"    // 收到且接通，以及拨出且接通（显示用）
    const val REJECTED_CALL = "Rejected call"    // 收到且用户挂断
    const val MISSED_CALL = "Missed call"       // 收到且未点接通

}

/**
 * 送礼场景
 * <AUTHOR>
 * @date 2025/08/05
 */
object SceneSource {
    const val IM_GIFT_PANEL = "im_gift_panel"        // im 礼物弹窗
    const val MOMENT_LIST = "moment_list"          // 帖子(信息流)列表
    const val MOMENT_PREVIEW = "moment_preview"       // 帖子(信息流)预览（过渡页-PPJoy）
    const val MOMENT_DETAIL = "moment_detail"        // 帖子(信息流)详情
    const val CALL_GIFT_PANEL = "call_gift_panel"      // 礼物弹窗送出
    const val CALL_ASK = "call_ask"             // 索要
    const val CALL_WISHLIST = "call_wishlist"        // 愿望清单
    const val CALL_FAST_GIVE = "call_fast_give"       // 快速送礼
    const val HOST_PROFILE = "host_profile"         // 主播详情页礼物弹窗（PPJoy）
    const val HOST_MEDIA = "host_media"           // 主播媒体浏览页（PPJoy）
}

/**
 * 挂断的场景
 */
object HangupScene {
    /**
     * 被呼叫页面主动挂断
     */
    const val ON_CALL_HANGUP = "oncall_hangup"

    /**
     * 呼叫页面主动挂断
     */
    const val CALLING_HANGUP = "calling_hangup"

    /**
     * 匹配页面主动挂断
     */
    const val FLASH_CHAT_HANGUP = "flashchat_hangup"

    /**
     * 视频通话页面主动挂断
     */
    const val CHATTING_HANGUP = "chatting_hangup"

    /**
     * 被呼叫页面超时挂断
     */
    const val ON_CALL_TIMEOUT = "oncall_timeout"

    /**
     * 呼叫页面超时挂断
     */
    const val CALLING_TIMEOUT = "timeout_calling_timeout" // 注意：您提供的原始值 "timeout_calling_timeout" 可能有误，通常超时场景会更简洁，例如 "calling_timeout"。请确认是否需要调整。

    /**
     * 匹配页面超时挂断
     */
    const val FLASH_CHAT_TIMEOUT = "flashchat_timeout"

    /**
     * 呼叫时对方正忙挂断
     */
    const val CALLING_ANCHOR_BUSY = "calling_anchor_busy"

    /**
     * 呼叫时接口报错
     */
    const val CALLING_API_ERROR = "calling_api_error"

}

/**
 * 用户类别
 */
object UserCategory {
    /**
     * 免费用户
     */
    const val FREE_USER = "free"
    /**
     * 付费用户
     */
    const val PAID_USER = "paid"
    /**
     * vip
     */
    const val VIP = "vip"
}