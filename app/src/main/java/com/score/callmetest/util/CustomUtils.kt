package com.score.callmetest.util

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.text.SpannableString
import android.text.Spanned
import android.text.style.ImageSpan
import androidx.core.content.ContextCompat
import com.opensource.svgaplayer.SVGACallback
import com.opensource.svgaplayer.SVGADrawable
import com.opensource.svgaplayer.SVGAImageView
import com.opensource.svgaplayer.SVGAParser
import com.score.callmetest.CallmeApplication
import com.score.callmetest.Constant
import com.score.callmetest.R
import com.score.callmetest.db.DatabaseFactory
import com.score.callmetest.manager.GlobalManager
import com.score.callmetest.network.PayChannelItem
import com.score.callmetest.network.UserInfo
import com.score.callmetest.ui.login.LoginActivity
import timber.log.Timber
import java.math.BigDecimal

/**
 * 当前项目抽离出公用的一些功能
 */
object CustomUtils {
    private const val TAG = "CustomUtils"

    /**
     *  播放一次svga
     *
     * @param [svgaView] view
     * @param [assetName] svga-name
     */
    fun playSvgaOnce(svgaView: SVGAImageView, assetName: String?) {
        playSvga(svgaView, assetName, 1)
    }

    /**
     *  播放svga
     *
     * @param [svgaView] view
     * @param [assetName] svga-name
     * @param [loops] 播放次数--默认无限
     * @param [onParsed] 解析完成回调
     * @param [onFinished] 播放完成回调
     * @param [onRepeat] 重复播放回调
     * @param [onError] 错误回调
     */
    fun playSvga(
        svgaView: SVGAImageView,
        assetName: String?,
        loops: Int = 0,
        onParsed: (() -> Unit)? = null,
        onFinished: (() -> Unit)? = null,
        onRepeat: (() -> Unit)? = null,
        onError: (() -> Unit)? = null
    ) {
        if (assetName == null) return

        ThreadUtils.runOnIO {
            SVGAParser.shareParser().apply {
                init(svgaView.context)
                decodeFromAssets(assetName, object : SVGAParser.ParseCompletion {
                    override fun onComplete(videoItem: com.opensource.svgaplayer.SVGAVideoEntity) {
                        ThreadUtils.runOnMain {
                            // 检查 View 是否还有效
                            if (svgaView.context == null) {
                                return@runOnMain
                            }

                            svgaView.setImageDrawable(SVGADrawable(videoItem))
                            svgaView.loops = loops
                            svgaView.startAnimation()
                            // 解析完成回调
                            onParsed?.invoke()
                            svgaView.callback = object : SVGACallback {
                                override fun onFinished() {
                                    onFinished?.invoke()
                                }

                                override fun onPause() {
                                }

                                override fun onRepeat() {
                                    onRepeat?.invoke()
                                }

                                override fun onStep(frame: Int, percentage: Double) {

                                }
                            }
                        }
                    }

                    override fun onError() {
                        ThreadUtils.runOnMain {
                            Timber.tag("dsc--").e("SVGA动画 $assetName 加载失败")
                            onError?.invoke()
                        }
                    }
                })
            }
        }
    }

    /**
     * 退出登录时清理所有本地数据、SDK资源，并跳转到登录页
     * @param [type] 类型  1-清理
     *                     0-正常退出
     */
    fun logoutAndClearData(type: Int = 0, context: Context) {
        GlobalManager.onLogout() // 重置全局状态 声网SDK登出
        //type == 1 注销账号，type == 0 登出
        if (type == 1) {
            GlobalManager.deleteUser()
            // 用协程在IO线程清空数据库，清理完成后再跳转
            ThreadUtils.runOnIO {
                try {
                    // 清空当前用户数据
                    DatabaseFactory.getDatabase(context).clearCurrentUserDatas()
                } catch (e: Exception) {
                    Timber.tag(TAG).e("删除数据库失败： ${e}")
                }
                gotoLogin(context)
            }
        } else if (type == 0) {

            gotoLogin(context)
        }
        else {
            Timber.tag(TAG).w("退出登录类型错误")
        }
    }

    /**
     * 转到登录
     * @param [context] 上下文
     */
    fun gotoLogin(context: Context) {
        if (ActivityUtils.getTopActivity() is LoginActivity) return
        val intent = Intent(context, LoginActivity::class.java)
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        context.startActivity(intent)
        if (context is Activity) {
            context.finish()
        }
    }
    /**
     * 根据商品类型和支付渠道计算折扣
     */
    fun calculateChannelDiscount(type: String?, thirdpartyCoinPercent: Int?, payChannel: PayChannelItem?): Int {
        if (payChannel == null) return 0
        return when (type) {
            "0" -> payChannel.presentCoinRatio ?: 0  // 普通商品
            "1" -> payChannel.promotionPresentCoinRatio ?: 0  // 促销商品
            "2" -> {  // 活动商品：取thirdpartyCoinPercent和promotionPresentCoinRatio的最大值
                val thirdpartyCoinPercentValue = thirdpartyCoinPercent ?: 0
                val promotionPresentCoinRatio = payChannel.promotionPresentCoinRatio ?: 0
                maxOf(thirdpartyCoinPercentValue, promotionPresentCoinRatio)
            }
            "3" -> payChannel.presentCoinRatio ?: 0  // 订阅商品
            else -> payChannel.presentCoinRatio ?: 0  // 默认使用普通商品的比例
        }
    }
    /**
     * 计算字符串的显示长度
     * 数字/字母/符号 = 1个字符，汉字 = 2个字符，emoji = 2个字符
     *
     * @param text 要计算的字符串
     * @return 显示长度
     */
    fun calculateDisplayLength(text: String): Int {
        var length = 0
        var i = 0
        
        while (i < text.length) {
            // 检查是否为emoji序列（包括复合emoji如国旗等）
            val emojiLength = getEmojiLength(text, i)
            if (emojiLength > 0) {
                length += 2  // emoji按2个字符计算
                i += emojiLength
                continue
            }
            
            val codePoint = text.codePointAt(i)
            val charCount = Character.charCount(codePoint)
            
            if (charCount == 1 && isChinese(text[i])) {
                length += 2  // 中文字符按2个字符计算
            } else {
                length += 1  // 其他字符按1个字符计算
            }
            
            i += charCount
        }
        
        return length
    }

    /**
     * 判断字符是否为中文字符
     *
     * @param char 要判断的字符
     * @return true表示是中文字符，false表示不是
     */
    private fun isChinese(char: Char): Boolean {
        val code = char.code
        // 中文字符的Unicode范围
        return (code in 0x4E00..0x9FFF) ||  // 基本汉字
                (code in 0x3400..0x4DBF) ||  // 扩展A
                (code in 0x20000..0x2A6DF) || // 扩展B
                (code in 0x2A700..0x2B73F) || // 扩展C
                (code in 0x2B740..0x2B81F) || // 扩展D
                (code in 0x2B820..0x2CEAF) || // 扩展E
                (code in 0x2CEB0..0x2EBEF) || // 扩展F
                (code in 0x30000..0x3134F) || // 扩展G
                (code in 0x3000..0x303F) ||   // 中文标点符号
                (code in 0xFF00..0xFFEF)      // 全角字符
    }
    
    /**
     * 获取从指定位置开始的emoji序列长度
     * 支持复合emoji如国旗、肤色修饰符、零宽连接符组合等
     *
     * @param text 文本字符串
     * @param startIndex 开始检查的位置
     * @return emoji序列的字符长度，如果不是emoji返回0
     */
    private fun getEmojiLength(text: String, startIndex: Int): Int {
        if (startIndex >= text.length) return 0
        
        var currentIndex = startIndex
        var emojiLength = 0
        var hasEmoji = false
        
        while (currentIndex < text.length) {
            val codePoint = text.codePointAt(currentIndex)
            val charCount = Character.charCount(codePoint)
            
            when {
                // 基本emoji字符
                isBasicEmoji(codePoint) -> {
                    hasEmoji = true
                    emojiLength += charCount
                    currentIndex += charCount
                }
                // 变体选择器（用于emoji样式）
                codePoint == 0xFE0F -> {
                    emojiLength += charCount
                    currentIndex += charCount
                }
                // 零宽连接符（用于组合emoji）
                codePoint == 0x200D -> {
                    emojiLength += charCount
                    currentIndex += charCount
                }
                // 肤色修饰符
                codePoint in 0x1F3FB..0x1F3FF -> {
                    emojiLength += charCount
                    currentIndex += charCount
                }
                // 区域指示符（用于国旗）
                codePoint in 0x1F1E0..0x1F1FF -> {
                    hasEmoji = true
                    emojiLength += charCount
                    currentIndex += charCount
                    // 国旗通常由两个区域指示符组成，继续检查下一个
                    if (currentIndex < text.length) {
                        val nextCodePoint = text.codePointAt(currentIndex)
                        if (nextCodePoint in 0x1F1E0..0x1F1FF) {
                            emojiLength += Character.charCount(nextCodePoint)
                            currentIndex += Character.charCount(nextCodePoint)
                        }
                    }
                    break // 国旗处理完成
                }
                // 标签字符（用于某些emoji序列）
                codePoint in 0xE0020..0xE007F -> {
                    emojiLength += charCount
                    currentIndex += charCount
                }
                // 取消标签字符
                codePoint == 0xE007F -> {
                    emojiLength += charCount
                    currentIndex += charCount
                }
                else -> {
                    // 遇到非emoji相关字符，结束检查
                    break
                }
            }
        }
        
        return if (hasEmoji) emojiLength else 0
    }
    
    /**
     * 判断Unicode码点是否为基本emoji字符
     *
     * @param codePoint Unicode码点
     * @return true表示是基本emoji，false表示不是
     */
    private fun isBasicEmoji(codePoint: Int): Boolean {
        return (codePoint in 0x1F600..0x1F64F) ||  // 表情符号
                (codePoint in 0x1F300..0x1F5FF) ||  // 杂项符号和象形文字
                (codePoint in 0x1F680..0x1F6FF) ||  // 交通和地图符号
                (codePoint in 0x1F700..0x1F77F) ||  // 炼金术符号
                (codePoint in 0x1F780..0x1F7FF) ||  // 几何形状扩展
                (codePoint in 0x1F800..0x1F8FF) ||  // 补充箭头-C
                (codePoint in 0x1F900..0x1F9FF) ||  // 补充符号和象形文字
                (codePoint in 0x1FA00..0x1FA6F) ||  // 扩展-A
                (codePoint in 0x1FA70..0x1FAFF) ||  // 符号和象形文字扩展-A
                (codePoint in 0x2600..0x26FF) ||    // 杂项符号
                (codePoint in 0x2700..0x27BF) ||    // 装饰符号
                (codePoint in 0x2B50..0x2B55) ||    // 星星等
                (codePoint == 0x2764) ||            // 红心
                (codePoint == 0x2763) ||            // 重感叹号
                (codePoint in 0x2194..0x21AA) ||    // 箭头
                (codePoint in 0x23E9..0x23F3) ||    // 播放按钮等
                (codePoint in 0x23F8..0x23FA) ||    // 暂停等按钮
                (codePoint == 0x2122) ||            // 商标符号
                (codePoint == 0x2139) ||            // 信息符号
                (codePoint in 0x2328..0x2328) ||    // 键盘
                (codePoint in 0x23CF..0x23CF) ||    // 弹出按钮
                (codePoint in 0x24C2..0x24C2) ||    // 圆圈M
                (codePoint in 0x25AA..0x25AB) ||    // 黑白方块
                (codePoint in 0x25B6..0x25B6) ||    // 播放按钮
                (codePoint in 0x25C0..0x25C0) ||    // 倒三角
                (codePoint in 0x25FB..0x25FE) ||    // 方块
                (codePoint in 0x2600..0x2604) ||    // 天气符号
                (codePoint == 0x260E) ||            // 电话
                (codePoint == 0x2611) ||            // 选中框
                (codePoint in 0x2614..0x2615) ||    // 雨伞、咖啡
                (codePoint == 0x2618) ||            // 三叶草
                (codePoint == 0x261D) ||            // 指向上的手指
                (codePoint == 0x2620) ||            // 骷髅
                (codePoint in 0x2622..0x2623) ||    // 放射性等
                (codePoint == 0x2626) ||            // 正统十字
                (codePoint == 0x262A) ||            // 星月符号
                (codePoint in 0x262E..0x262F) ||    // 和平符号
                (codePoint in 0x2638..0x2639) ||    // 法轮、笑脸
                (codePoint in 0x263A..0x263A) ||    // 笑脸
                (codePoint == 0x2648) ||            // 白羊座
                (codePoint in 0x2650..0x2653) ||    // 其他星座
                (codePoint == 0x2660) ||            // 黑桃
                (codePoint == 0x2663) ||            // 梅花
                (codePoint in 0x2665..0x2666) ||    // 红心、方块
                (codePoint == 0x2668) ||            // 温泉
                (codePoint == 0x267B) ||            // 回收符号
                (codePoint in 0x267E..0x267F) ||    // 无限、轮椅
                (codePoint in 0x2692..0x2697) ||    // 工具等
                (codePoint == 0x2699) ||            // 齿轮
                (codePoint in 0x269B..0x269C) ||    // 原子等
                (codePoint in 0x26A0..0x26A1) ||    // 警告、闪电
                (codePoint == 0x26AA) ||            // 白圆
                (codePoint == 0x26AB) ||            // 黑圆
                (codePoint in 0x26B0..0x26B1) ||    // 棺材等
                (codePoint in 0x26BD..0x26BE) ||    // 足球、棒球
                (codePoint in 0x26C4..0x26C5) ||    // 雪人、云
                (codePoint == 0x26C8) ||            // 雷雨
                (codePoint == 0x26CE) ||            // 蛇夫座
                (codePoint == 0x26CF) ||            // 镐
                (codePoint == 0x26D1) ||            // 头盔
                (codePoint in 0x26D3..0x26D4) ||    // 链条等
                (codePoint in 0x26E9..0x26EA) ||    // 神社、教堂
                (codePoint in 0x26F0..0x26F5) ||    // 山、船
                (codePoint in 0x26F7..0x26FA) ||    // 滑雪者等
                (codePoint == 0x26FD)               // 加油站
    }

    /**
     * 检查字符串的显示长度是否超过限制
     *
     * @param text 要检查的字符串
     * @param maxDisplayLength 最大显示长度限制
     * @return true表示超过限制，false表示未超过
     */
    fun isDisplayLengthExceeded(text: String, maxDisplayLength: Int): Boolean {
        return calculateDisplayLength(text) > maxDisplayLength
    }

    /**
     * 截取字符串到指定的显示长度
     *
     * @param text 原始字符串
     * @param maxDisplayLength 最大显示长度
     * @return 截取后的字符串
     */
    fun truncateToDisplayLength(text: String, maxDisplayLength: Int): String {
        var currentLength = 0
        val result = StringBuilder()
        var i = 0
        
        while (i < text.length) {
            // 检查是否为emoji序列
            val emojiLength = getEmojiLength(text, i)
            if (emojiLength > 0) {
                if (currentLength + 2 > maxDisplayLength) {
                    break
                }
                result.append(text.substring(i, i + emojiLength))
                currentLength += 2
                i += emojiLength
                continue
            }
            
            val codePoint = text.codePointAt(i)
            val charCount = Character.charCount(codePoint)
            
            // 计算当前字符的显示长度
            val charLength = if (charCount == 1 && isChinese(text[i])) 2 else 1
            
            if (currentLength + charLength > maxDisplayLength) {
                break
            }
            
            // 添加完整的字符
            if (charCount == 1) {
                result.append(text[i])
            } else {
                result.append(text.substring(i, i + charCount))
            }
            
            currentLength += charLength
            i += charCount
        }
        
        return result.toString()
    }
    /**
     * 排行榜截取昵称（汉字算2个字符）
     * 默认8个字符
     * 超出时显示省略号
     */
    fun rankTruncateNickname(nickname: String,maxDisplayLength: Int = 8): String {
        return try {
            val maxDisplayLength = maxDisplayLength
            if (calculateDisplayLength(nickname) <= maxDisplayLength) {
                nickname
            } else {
                truncateToDisplayLength(nickname, maxDisplayLength - 2) + ".."
            }
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "截断昵称失败")
            nickname
        }
    }

    /**
     * 截取字符串到指定的显示长度,中文算2个字符
     *
     * @param name 要截取的字符串
     * @return 截取后的字符串
     */
    fun truncateName(name: String): String {
        val truncatedName = truncateToDisplayLength(name, 20)
        return truncatedName
    }

    /**
     * 根据礼物名称获取对应的资源ID
     *
     * @param giftCode 礼物code
     * @return 资源ID
     */
    fun getGiftResIdById(giftCode: String?): Int {
        // 这里实现根据礼物名称查找资源ID的逻辑
        return when (giftCode) {
            "1" -> R.drawable.kiss  // 文档香蕉、kiss、青瓜都有。。
            "2" -> R.drawable.meigui // 一支玫瑰、巧克力
            "3" -> R.drawable.bear // 熊
            "4" -> R.drawable.lipstick // 唇膏
            "5" -> R.drawable.xiangbin // 香槟、香水
            "6" -> R.drawable.rose // 一束玫瑰
            "7" -> R.drawable.shouzhuo  // 表耳环手镯--我选手镯
            "8" -> R.drawable.crystal_shoes // 水晶鞋
            "9" -> R.drawable.yongyi // 泳衣
            "10" -> R.drawable.hunsha // 婚纱
            "11" -> R.drawable.aimashibao // 爱马仕包
            "12" -> R.drawable.ring // 戒指
            "13" -> R.drawable.chuang // 床
            "14" -> R.drawable.xianglian // 砖石项链
            "15" -> R.drawable.huangguan // 皇冠
            "16" -> R.drawable.paoche // 跑车
            "17" -> R.drawable.bieshu // 别墅
            "18" -> R.drawable.feiji // 飞机
            "19" -> R.drawable.youlun // 油轮
            "20" -> R.drawable.chengbao // 城堡
            else -> R.drawable.gift_placehold
        }
    }

    /**
     * 创建带金币图标的SpannableString - 完整版本
     *
     * @param context 上下文
     * @param text 原始文本，使用"icon"作为占位符，或者使用insertPosition指定插入位置
     * @param coinSizeDp 金币图标大小（dp），默认14dp
     * @param alignment 图标对齐方式，默认ALIGN_BOTTOM
     * @param spacesBefore 金币图标前的空格数量
     * @param spacesAfter 金币图标后的空格数量
     * @param insertPosition 指定插入位置（可选），如果为null则查找"icon"占位符
     * @return 包含金币图标的SpannableString
     */
    fun createCoinSpannableText(
        context: Context,
        text: String,
        coinSizeDp: Float = 14f,
        alignment: Int = ImageSpan.ALIGN_BOTTOM,
        spacesBefore: Int = 0,
        spacesAfter: Int = 0,
        insertPosition: Int? = null
    ): SpannableString {
        // 构建最终文本，添加前后空格
        val spacesBeforeStr = " ".repeat(spacesBefore)
        val spacesAfterStr = " ".repeat(spacesAfter)

        val finalText: String
        val iconPosition: Int

        if (insertPosition != null) {
            // 使用指定位置插入
            finalText = text.substring(0, insertPosition) +
                       spacesBeforeStr + " " + spacesAfterStr +
                       text.substring(insertPosition)
            iconPosition = insertPosition + spacesBefore
        } else {
            // 查找并替换"icon"占位符
            val iconIndex = text.indexOf("icon")
            if (iconIndex == -1) {
                // 如果没有找到"icon"，直接返回原文本
                return SpannableString(text)
            }

            finalText = text.replace("icon", spacesBeforeStr + " " + spacesAfterStr)
            iconPosition = iconIndex + spacesBefore
        }

        val spannable = SpannableString(finalText)

        // 获取金币图标drawable
        val coinDrawable = ContextCompat.getDrawable(context, R.drawable.coin)
        coinDrawable?.let { drawable ->
            // 设置图标大小
            val iconSize = DisplayUtils.dp2px(coinSizeDp)
            drawable.setBounds(0, 0, iconSize, iconSize)

            // 创建ImageSpan并插入到指定位置
            val imageSpan = ImageSpan(drawable, alignment)
            spannable.setSpan(
                imageSpan,
                iconPosition,
                iconPosition + 1,
                Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
            )
        }

        return spannable
    }

    /**
     * 比较价格
     * @param [price1] price1
     * @param [price2] price2
     * @return [Int]
     */
    fun comparePrice(price1: String, price2: String): Int {
        // 1. 移除所有非数字字符（保留小数点）
        val clean1 = price1.filter { it.isDigit() || it == '.' }
        val clean2 = price2.filter { it.isDigit() || it == '.' }

        // 2. 转换为 BigDecimal（避免浮点精度问题）
        val price1 = clean1.toBigDecimalOrNull() ?: BigDecimal.ZERO
        val price2 = clean2.toBigDecimalOrNull() ?: BigDecimal.ZERO

        // 3. 返回标准比较结果（-1, 0, 1）
        return price1.compareTo(price2)
    }

    /**
     * 提供机器人客服info
     * @return [UserInfo]
     */
    fun provideCustomService(): UserInfo {
        return UserInfo(
            userId = Constant.ROBOt_ID,
            nickname = CallmeApplication.context.getString(R.string.msg_item_service_name),
        )
    }

}