package com.score.callmetest.util

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.Rect
import android.renderscript.Allocation
import android.renderscript.Element
import android.renderscript.RenderScript
import android.renderscript.ScriptIntrinsicBlur
import android.view.View
import android.widget.ImageView
import androidx.annotation.RequiresApi
import timber.log.Timber

/**
 * 高斯模糊工具类
 * 用于对View或Bitmap进行高斯模糊处理
 */
object BlurUtils {
    
    private const val TAG = "BlurUtils"
    
    /**
     * 对View进行高斯模糊处理
     * @param view 需要模糊的View
     * @param radius 模糊半径 (0.0f - 25.0f)
     * @param scale 缩放比例，用于提高性能
     * @return 模糊后的Bitmap
     */
    fun blurView(view: View, radius: Float = 15.0f, scale: Float = 0.1f): Bitmap? {
        return try {
            // 创建View的Bitmap
            val bitmap = createBitmapFromView(view, scale)
            // 对Bitmap进行模糊处理
            blurBitmap(view.context, bitmap, radius)
        } catch (e: Exception) {
            Timber.tag(TAG).e("模糊处理失败: ${e.message}")
            null
        }
    }
    
    /**
     * 对Bitmap进行高斯模糊处理
     * @param context 上下文
     * @param bitmap 需要模糊的Bitmap
     * @param radius 模糊半径 (0.0f - 25.0f)
     * @return 模糊后的Bitmap
     */
    fun blurBitmap(context: Context, bitmap: Bitmap, radius: Float = 15.0f): Bitmap? {
        return try {
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.JELLY_BEAN_MR1) {
                blurBitmapWithRenderScript(context, bitmap, radius)
            } else {
                // 对于低版本Android，使用简单的模糊算法
                blurBitmapSimple(bitmap, radius)
            }
        } catch (e: Exception) {
            Timber.tag(TAG).e("Bitmap模糊处理失败: ${e.message}")
            null
        }
    }
    
    /**
     * 使用RenderScript进行高斯模糊（API 17+）
     */
    @RequiresApi(17)
    private fun blurBitmapWithRenderScript(context: Context, bitmap: Bitmap, radius: Float): Bitmap? {
        var renderScript: RenderScript? = null
        var input: Allocation? = null
        var output: Allocation? = null
        var scriptIntrinsicBlur: ScriptIntrinsicBlur? = null
        
        try {
            renderScript = RenderScript.create(context)
            input = Allocation.createFromBitmap(renderScript, bitmap)
            output = Allocation.createTyped(renderScript, input.type)
            scriptIntrinsicBlur = ScriptIntrinsicBlur.create(renderScript, Element.U8_4(renderScript))
            
            scriptIntrinsicBlur.setRadius(radius.coerceIn(0.0f, 25.0f))
            scriptIntrinsicBlur.setInput(input)
            scriptIntrinsicBlur.forEach(output)
            
            // 创建可变的Bitmap副本
            val blurredBitmap = bitmap.copy(Bitmap.Config.ARGB_8888, true) ?: run {
                Timber.tag(TAG).e("无法创建Bitmap副本")
                return null
            }
            output.copyTo(blurredBitmap)
            
            return blurredBitmap
        } catch (e: Exception) {
            Timber.tag(TAG).e("RenderScript模糊处理失败: ${e.message}")
            return null
        } finally {
            // 清理资源
            renderScript?.destroy()
            input?.destroy()
            output?.destroy()
            scriptIntrinsicBlur?.destroy()
        }
    }
    
    /**
     * 简单的模糊算法（用于低版本Android）
     */
    private fun blurBitmapSimple(bitmap: Bitmap, radius: Float): Bitmap {
        val width = bitmap.width
        val height = bitmap.height
        
        // 检查Bitmap尺寸
        if (width <= 0 || height <= 0) {
            Timber.tag(TAG).e("Bitmap尺寸无效: width=$width, height=$height")
            return bitmap
        }
        
        val blurredBitmap = bitmap.copy(Bitmap.Config.ARGB_8888, true) ?: run {
            Timber.tag(TAG).e("无法创建Bitmap副本，使用原始Bitmap")
            return bitmap
        }
        
        val pixels = IntArray(width * height)
        blurredBitmap.getPixels(pixels, 0, width, 0, 0, width, height)
        
        val kernelSize = (radius * 2 + 1).toInt()
        val kernel = createGaussianKernel(kernelSize, radius)
        
        val newPixels = IntArray(width * height)
        
        for (y in 0 until height) {
            for (x in 0 until width) {
                var r = 0
                var g = 0
                var b = 0
                var a = 0
                var weightSum = 0f
                
                for (ky in 0 until kernelSize) {
                    for (kx in 0 until kernelSize) {
                        val px = (x + kx - kernelSize / 2).coerceIn(0, width - 1)
                        val py = (y + ky - kernelSize / 2).coerceIn(0, height - 1)
                        val pixelIndex = py * width + px
                        
                        // 检查像素索引是否有效
                        if (pixelIndex >= 0 && pixelIndex < pixels.size) {
                            val pixel = pixels[pixelIndex]
                            val weight = kernel[ky * kernelSize + kx]
                            
                            r += (((pixel shr 16) and 0xFF) * weight).toInt()
                            g += (((pixel shr 8) and 0xFF) * weight).toInt()
                            b += ((pixel and 0xFF) * weight).toInt()
                            a += (((pixel shr 24) and 0xFF) * weight).toInt()
                            weightSum += weight
                        }
                    }
                }
                
                // 避免除零错误
                if (weightSum > 0) {
                    r = (r / weightSum).toInt().coerceIn(0, 255)
                    g = (g / weightSum).toInt().coerceIn(0, 255)
                    b = (b / weightSum).toInt().coerceIn(0, 255)
                    a = (a / weightSum).toInt().coerceIn(0, 255)
                } else {
                    // 如果权重和为0，使用原始像素值
                    val originalPixel = pixels[y * width + x]
                    r = (originalPixel shr 16) and 0xFF
                    g = (originalPixel shr 8) and 0xFF
                    b = originalPixel and 0xFF
                    a = (originalPixel shr 24) and 0xFF
                }
                
                newPixels[y * width + x] = (a shl 24) or (r shl 16) or (g shl 8) or b
            }
        }
        
        blurredBitmap.setPixels(newPixels, 0, width, 0, 0, width, height)
        return blurredBitmap
    }
    
    /**
     * 创建高斯核
     */
    private fun createGaussianKernel(size: Int, sigma: Float): FloatArray {
        val kernel = FloatArray(size * size)
        val center = size / 2
        var sum = 0f
        
        for (y in 0 until size) {
            for (x in 0 until size) {
                val dx = x - center
                val dy = y - center
                val distance = dx * dx + dy * dy
                val value = kotlin.math.exp(-distance / (2 * sigma * sigma))
                kernel[y * size + x] = value
                sum += value
            }
        }
        
        // 归一化
        for (i in kernel.indices) {
            kernel[i] /= sum
        }
        
        return kernel
    }
    
    /**
     * 从View创建Bitmap
     */
    private fun createBitmapFromView(view: View, scale: Float): Bitmap {
        val width = (view.width * scale).toInt()
        val height = (view.height * scale).toInt()
        
        if (width <= 0 || height <= 0) {
            throw IllegalArgumentException("View尺寸无效: width=$width, height=$height")
        }
        
        val bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(bitmap)
        
        // 缩放Canvas
        canvas.scale(scale, scale)
        
        // 绘制View到Canvas
        view.draw(canvas)
        
        return bitmap
    }
    
    /**
     * 对ImageView设置模糊效果
     * @param imageView 目标ImageView
     * @param originalBitmap 原始Bitmap
     * @param radius 模糊半径
     */
    fun setBlurredImage(imageView: ImageView, originalBitmap: Bitmap, radius: Float = 15.0f) {
        try {
            val blurredBitmap = blurBitmap(imageView.context, originalBitmap, radius)
            if (blurredBitmap != null) {
                imageView.setImageBitmap(blurredBitmap)
                Timber.tag(TAG).d("成功设置模糊图片")
            } else {
                Timber.tag(TAG).w("模糊处理失败，使用原始图片")
                imageView.setImageBitmap(originalBitmap)
            }
        } catch (e: Exception) {
            Timber.tag(TAG).e("设置模糊图片失败: ${e.message}")
            imageView.setImageBitmap(originalBitmap)
        }
    }
    
    /**
     * 对View应用模糊遮罩效果
     * @param view 需要模糊的View
     * @param radius 模糊半径
     * @param overlayAlpha 遮罩透明度 (0-255)
     */
    fun applyBlurOverlay(view: View, radius: Float = 15.0f, overlayAlpha: Int = 128) {
        try {
            val blurredBitmap = blurView(view, radius)
            if (blurredBitmap != null) {
                // 创建带透明度的遮罩
                val overlayBitmap = Bitmap.createBitmap(
                    blurredBitmap.width,
                    blurredBitmap.height,
                    Bitmap.Config.ARGB_8888
                )
                val canvas = Canvas(overlayBitmap)
                val paint = Paint().apply {
                    alpha = overlayAlpha
                }
                canvas.drawBitmap(blurredBitmap, 0f, 0f, paint)
                
                // 这里可以根据需要设置遮罩效果
                Timber.tag(TAG).d("成功应用模糊遮罩效果")
            }
        } catch (e: Exception) {
            Timber.tag(TAG).e("应用模糊遮罩失败: ${e.message}")
        }
    }
}
