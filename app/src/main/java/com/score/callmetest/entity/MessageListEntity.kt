package com.score.callmetest.entity

import android.os.Parcelable
import com.score.callmetest.CallStatus
import com.score.callmetest.CallmeApplication
import com.score.callmetest.Constant
import com.score.callmetest.R
import com.score.callmetest.manager.UserInfoManager
import com.score.callmetest.network.BroadcasterModel
import kotlinx.parcelize.Parcelize

/**
 * 消息列表数据模型
 * @property userId 用户ID
 * @property currentUserId 当前用户ID
 * @property userName 用户名称
 * @property gender 性别
 * @property unitPrice 价格
 * @property avatar 头像URL
 * @property avatarThumbUrl 头像URL
 * @property lastMessage 最后一条消息内容
 * @property lastMessageType 最后一条消息内容类型
 * @property timestamp 时间戳显示文本
 * @property timeInMillis 时间戳毫秒值，用于排序
 * @property unreadCount 未读消息数
 * @property onlineStatus 在线状态
 * @property isPinned 是否置顶
 * @property isBottomView 是否是底部item
 */
@Parcelize
data class MessageListEntity(
    val userId: String,
    val currentUserId: String,
    val userName: String,
    val gender: Int,
    val unitPrice: Int,
    var avatar: String,
    var avatarThumbUrl: String,
    var lastMessage: String,
    var lastMessageType: MessageType,
    var timestamp: String,
    var timeInMillis: Long = System.currentTimeMillis(),
    var unreadCount: Int = 0,
    var onlineStatus: String = CallStatus.UNKNOWN,
    var isPinned: Boolean = false,
    var isBottomView: Boolean = false
) : Parcelable{

    companion object{

        fun provideCustomService(): MessageListEntity{
            return MessageListEntity(
                userId = Constant.ROBOt_ID,
                currentUserId = UserInfoManager.myUserInfo?.userId?:"",
                userName = CallmeApplication.context.getString(R.string.msg_item_service_name),
                gender = 0,
                unitPrice = 0,
                avatar = "",
                avatarThumbUrl = "",
                lastMessage = "",
                lastMessageType = MessageType.ROBOT,
                timestamp = "",
                onlineStatus = CallStatus.UNKNOWN,
                isPinned = true,
            )
        }
        fun provideBottomView(): MessageListEntity {
            return MessageListEntity(
                userId = "",
                currentUserId = UserInfoManager.myUserInfo?.userId?:"",
                userName = "",
                gender = 0,
                unitPrice = 0,
                avatar = "",
                avatarThumbUrl = "",
                lastMessage = "",
                lastMessageType = MessageType.TEXT,
                timestamp = "",
                timeInMillis = 0,
                unreadCount = 0,
                onlineStatus = CallStatus.UNKNOWN,
                isPinned = false,
                isBottomView = true
            )
        }

        fun provideVisitorItem(): MessageListEntity {
            return MessageListEntity(
                userId = "",
                currentUserId = UserInfoManager.myUserInfo?.userId?:"",
                userName = "",
                gender = 0,
                unitPrice = 0,
                avatar = "",
                avatarThumbUrl = "",
                lastMessage = "",
                lastMessageType = MessageType.VISITOR,
                timestamp = "",
                timeInMillis = 0,
                unreadCount = 0,
                onlineStatus = CallStatus.UNKNOWN,
                isPinned = false,
                isBottomView = false
            )
        }
    }

    fun toBroadcasterModel() : BroadcasterModel {
        return BroadcasterModel(
            userId = this.userId,
            avatar = this.avatar,
            avatarThumbUrl = this.avatarThumbUrl,
            nickname = this.userName,
            countdown = null, // 倒计时,  user里没有
            gender = this.gender,
            callCoins = this.unitPrice,
            videoMapPaths = null // 不需要
        )
    }

}

/**
 * 是否是机器人客服
 * @return [Boolean]
 */
fun MessageListEntity.isRobot(): Boolean{
    return this.lastMessageType == MessageType.ROBOT ||
            this.userId == Constant.ROBOt_ID
}

/**
 * 是否采访者
 */
fun MessageListEntity.isVisitor(): Boolean {
    return this.lastMessageType == MessageType.VISITOR
}