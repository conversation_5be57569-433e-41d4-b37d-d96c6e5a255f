package com.score.callmetest.ui.home.ranklist

import android.content.Intent
import android.view.LayoutInflater
import android.view.View
import android.view.View.GONE
import android.view.View.OVER_SCROLL_NEVER
import android.view.View.VISIBLE
import android.view.ViewGroup
import android.widget.ImageView
import androidx.core.graphics.toColorInt
import androidx.core.view.doOnPreDraw
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.score.callmetest.Constant
import com.score.callmetest.R
import com.score.callmetest.databinding.FragmentCoupleListBinding
import com.score.callmetest.manager.UserInfoManager
import com.score.callmetest.network.RankData
import com.score.callmetest.network.UserRankModel
import com.score.callmetest.network.toBroadcasterModel
import com.score.callmetest.ui.base.BaseFragment
import com.score.callmetest.ui.broadcaster.BroadcasterDetailActivity
import com.score.callmetest.ui.home.ranklist.adapter.CoupleListAdapter
import com.score.callmetest.util.CustomUtils
import com.score.callmetest.util.DisplayUtils
import com.score.callmetest.util.DrawableUtils
import com.score.callmetest.util.GlideUtils
import com.score.callmetest.util.click
import timber.log.Timber

/**
 * 情侣排行榜Fragment
 * 显示情侣排行榜数据，包括前三名展示和列表
 */
class CoupleListFragment : BaseFragment<FragmentCoupleListBinding, RankListViewModel>() {

    private lateinit var mAdapter: CoupleListAdapter
    private val TAG = "CoupleListFragment"
    private var mEmptyView: View? = null

    override fun getViewBinding(inflater: LayoutInflater, container: ViewGroup?): FragmentCoupleListBinding {
        return FragmentCoupleListBinding.inflate(inflater, container, false)
    }

    override fun getViewModelClass(): Class<RankListViewModel> {
        return RankListViewModel::class.java
    }

    override fun initView() {
        super.initView()
        
        // 获取Activity的ViewModel实例，确保数据共享
        viewModel = ViewModelProvider(requireActivity())[RankListViewModel::class.java]
        
        setupRecyclerView()
        setupPersonalRankInfo()
        setupClickListeners()
        // 设置时间背景
        binding.lineRichTime.background = DrawableUtils.createRoundRectDrawableWithStroke(
            fillColor = "#30ffffff".toColorInt(),
            radius = DisplayUtils.dp2pxInternalFloat(19f),
            strokeColor = "#4affffff".toColorInt(),
            strokeWidth = DisplayUtils.dp2pxInternal(1f)
        )

        binding.mainContentLayout.doOnPreDraw {
            initMainContentLayout()
            setupRankTopAnimation()
        }
    }

    private fun initMainContentLayout() {
        val screenHeight = binding.rootView.height
        // 限制BottomSheet最大高度为屏幕高度-250dp
        val maxHeight = screenHeight - DisplayUtils.dp2px(250f)

        val params = binding.mainContentLayout.layoutParams
        params.height = maxHeight
        binding.mainContentLayout.layoutParams = params

        val behavior = BottomSheetBehavior.from(binding.mainContentLayout)
        // 设置初始高度为屏幕的55%
        behavior.peekHeight = screenHeight * 55 / 100
        behavior.state = BottomSheetBehavior.STATE_COLLAPSED
    }

    override fun initData() {
        super.initData()

        // 观察情侣排行榜数据
        viewModel.userCoupleRankData.observe(this) { rankResponse ->
            rankResponse?.let { data ->

                // 更新月份显示
                data.monthName?.let { monthName ->
                    binding.tvTime.text = monthName
                }
                
                // 更新排行榜列表数据
                data.rankData?.let { rankList ->
                    // 检查数据是否为空
                    if (rankList.isEmpty()) {
                        updateEmptyViewVisibility(true)
                    } else {
                        updateEmptyViewVisibility(false)
                        // 从第4名开始展示
                        mAdapter.submitRankList(rankList)

                        // 更新前三名显示
                        updateTopThreeDisplay(rankList)

                        // 检查用户是否在榜上，更新个人排名信息显示
                        updatePersonalRankInfoWithData(rankList, data.sortNo)
                    }
                } ?: run {
                    // rankData为null时显示空页面
                    updateEmptyViewVisibility(true)
                }
                
                // 更新个人排名
                data.sortNo?.let { sortNo ->
                    binding.tvMineRank.text = sortNo
                }
            }
        }

        // 观察错误信息
        viewModel.errorMessage.observe(this) { errorMessage ->
            errorMessage?.let { error ->
                if (error.second == 2) {
                    Timber.tag(TAG).e("情侣排行榜数据加载错误 ${error.first}")
                    // 错误时显示空页面
                    updateEmptyViewVisibility(true)
                }

            }
        }
    }

    /**
     * 设置RecyclerView
     */
    private fun setupRecyclerView() {
        mAdapter = CoupleListAdapter()
        
        binding.rvCouple.apply {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = mAdapter
            // 禁用过度滚动效果
            overScrollMode = OVER_SCROLL_NEVER
        }
        
        // 设置列表项点击事件
        mAdapter.setOnItemClickListener { rankData ->
            // 只能跳转主播 broadcastetModel
            val targetUserModel = rankData.broadcastetModel 
            targetUserModel?.let { userModel ->
                navigateToBroadcasterDetail(userModel)
            } ?: run {
                Timber.tag(TAG).w("主播数据为空")
            }
        }
        
        // 设置头像点击事件
        mAdapter.setOnAvatarClickListener { userRankModel ->
            navigateToBroadcasterDetail(userRankModel)
        }
    }
    
    /**
     * 设置rank_top的Svga动画
     */
    private fun setupRankTopAnimation() {
        // 播放rank_top.svga动画
        CustomUtils.playSvga(binding.rankTop, "rank_top.svga")
    }
    
    /**
     * 设置个人排名信息
     */
    private fun setupPersonalRankInfo() {
        // 先设置默认状态
        val myUserInfo = UserInfoManager.myUserInfo
        myUserInfo?.let { userInfo ->
            // 默认显示个人信息
            binding.tvMineName.text = CustomUtils.rankTruncateNickname(userInfo.nickname ?: "Unknown",30)
            
            // 默认显示个人头像
            userInfo.avatarThumbUrl?.let { avatarUrl ->
                GlideUtils.load(
                    binding.tvCoupleAvatar1,
                    avatarUrl,
                    placeholder = R.drawable.placeholder,
                    error = R.drawable.placeholder,
                    isCircle = true
                )
            } ?: run {
                binding.tvCoupleAvatar1.setImageResource(R.drawable.placeholder)
            }
            
            // 默认隐藏第二个头像（主播头像）
            binding.tvCoupleAvatar2.visibility = GONE
            
        } ?: run {
            Timber.tag(TAG).w("未获取到用户信息")
            binding.tvMineName.text = "Unknown"
            binding.tvCoupleAvatar1.setImageResource(R.drawable.placeholder)
            binding.tvCoupleAvatar2.visibility = GONE
        }

    }

    /**
     * 设置点击事件监听器
     */
    private fun setupClickListeners() {
        // 第一名点击事件
        val firstClickListener = View.OnClickListener {
            handleTopRankClick(0)
        }
        binding.ivAvatar1Couple2.setOnClickListener(firstClickListener)


        // 第二名点击事件
        val secondClickListener = View.OnClickListener {
            handleTopRankClick(1)
        }
        binding.ivAvatar2Couple2.setOnClickListener(secondClickListener)

        // 第三名点击事件
        val thirdClickListener = View.OnClickListener {
            handleTopRankClick(2)
        }
        binding.ivAvatar3Couple2.setOnClickListener(thirdClickListener)
    }
    
    /**
     * 处理前三名排行榜点击事件
     */
    private fun handleTopRankClick(rankIndex: Int) {
        try {
            viewModel.userCoupleRankData.value?.rankData?.getOrNull(rankIndex)?.let { rankData ->
                // 只有主播能够点击 broadcastetModel，
                val targetUserModel = rankData.broadcastetModel
                targetUserModel?.let { userModel ->
                    navigateToBroadcasterDetail(userModel)
                } ?: run {
                    Timber.tag(TAG).w("排行榜第${rankIndex + 1}名数据为空")
                }
            }
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "处理排行榜第${rankIndex + 1}名点击事件失败")
        }
    }

    /**
     * 导航到主播详情页
     */
    private fun navigateToBroadcasterDetail(userRankModel: UserRankModel) {
        try {
            Timber.tag(TAG).d("跳转主播详情页，userRankModel=$userRankModel")
            userRankModel.userId?.let { userId ->
                Timber.tag(TAG).d("跳转主播详情页，userId=$userId")
                
                // 先获取完整的用户信息
                UserInfoManager.getUserInfo(
                    userId = userId.toString(),
                    scope = lifecycleScope,
                    forceUpdate = true
                ) { userInfo ->
                    if (userInfo != null) {
                        // 获取到完整信息后跳转
                        val intent = Intent(requireContext(), BroadcasterDetailActivity::class.java)
                        intent.putExtra(Constant.BROADCASTER_MODEL,userInfo.toBroadcasterModel())
                        startActivity(intent)
                    } else {
                        // 获取用户信息失败
                        Timber.tag(TAG).w("获取完整用户信息失败，userId=$userId")
                    }
                }
            } ?: run {
                Timber.tag(TAG).w("用户ID为空，无法跳转")
            }
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "跳转主播详情页失败")
        }
    }

    /**
     * 更新前三名显示
     */
    private fun updateTopThreeDisplay(rankList: List<RankData>) {
        try {
            // 第一名
            if (rankList.isNotEmpty()) {
                val firstRankData = rankList[0]
                val userModel = firstRankData.userModel
                val broadcasterModel = firstRankData.broadcastetModel
                
                // 显示两个用户的昵称组合
                val nickname1 = CustomUtils.rankTruncateNickname(userModel?.nickname ?: "Unknown",10)
                val nickname2 = CustomUtils.rankTruncateNickname(broadcasterModel?.nickname ?: "Unknown",10)
                binding.tvAvatar1Name.text = if (userModel != broadcasterModel) "$nickname1 & $nickname2" else nickname1
                
                // 加载两个头像
                loadAvatar(userModel, binding.ivAvatar1Couple1)
                loadAvatar(broadcasterModel, binding.ivAvatar1Couple2)
            }
            
            // 第二名
            if (rankList.size > 1) {
                val secondRankData = rankList[1]
                val userModel = secondRankData.userModel 
                val broadcasterModel = secondRankData.broadcastetModel
                
                val nickname1 = CustomUtils.rankTruncateNickname(userModel?.nickname ?: "Unknown")
                val nickname2 = CustomUtils.rankTruncateNickname(broadcasterModel?.nickname ?: "Unknown")
                binding.tvAvatar2Name.text = if (userModel != broadcasterModel) "$nickname1 & $nickname2" else nickname1
                
                loadAvatar(userModel, binding.ivAvatar2Couple1)
                loadAvatar(broadcasterModel, binding.ivAvatar2Couple2)
            }
            
            // 第三名
            if (rankList.size > 2) {
                val thirdRankData = rankList[2]
                val userModel = thirdRankData.userModel 
                val broadcasterModel = thirdRankData.broadcastetModel
                
                val nickname1 = CustomUtils.rankTruncateNickname(userModel?.nickname ?: "Unknown")
                val nickname2 = CustomUtils.rankTruncateNickname(broadcasterModel?.nickname ?: "Unknown")
                binding.tvAvatar3Name.text = if (userModel != broadcasterModel) "$nickname1 & $nickname2" else nickname1
                
                loadAvatar(userModel, binding.ivAvatar3Couple1)
                loadAvatar(broadcasterModel, binding.ivAvatar3Couple2)
            }
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "更新前三名显示时出错")
        }
    }

    /**
     * 统一头像加载方法
     */
    private fun loadAvatar(userRankModel: UserRankModel?, imageView: ImageView) {
        if (userRankModel == null) {
            imageView.setImageResource(R.drawable.placeholder)
            return
        }
        
        // 优先使用avatar，其次使用avatarMapPath，最后使用默认头像
        val avatarUrl = userRankModel.avatar ?: userRankModel.avatarMapPath
        
        if (!avatarUrl.isNullOrBlank()) {
            GlideUtils.load(
                imageView,
                avatarUrl,
                placeholder = R.drawable.placeholder,
                error = R.drawable.placeholder,
                isCircle = true
            )
        } else {
            imageView.setImageResource(R.drawable.placeholder)
        }
    }

    /**
     * 更新空页面的可见性
     * @param show true: 显示空页面, false: 隐藏空页面
     */
    private fun updateEmptyViewVisibility(show: Boolean) {
        try {
            if (show) {
                // 确保空状态视图已经被inflate
                if (mEmptyView == null) {
                    mEmptyView = binding.emptyView.inflate()
                }
                // 显示空状态视图，并隐藏列表
                mEmptyView?.visibility = VISIBLE
                binding.rvCouple.visibility = GONE
            } else {
                // 隐藏空状态视图，并显示列表
                mEmptyView?.visibility = GONE
                binding.rvCouple.visibility = VISIBLE
            }
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "更新空页面可见性失败")
        }
    }
    
    /**
     * 根据排行榜数据更新个人排名信息显示
     * 如果用户在榜上，显示榜上的数据；不在榜上，显示个人信息
     */
    private fun updatePersonalRankInfoWithData(rankList: List<RankData>, sortNo: String?) {
        try {
            val myUserInfo = UserInfoManager.myUserInfo
            val myUserId = myUserInfo?.userId
            
            if (myUserId == null) {
                Timber.tag(TAG).w("当前用户ID为空，无法检查排行榜状态")
                return
            }
            
            // 检查用户是否在排行榜上
            val myRankData = rankList.find { rankData ->
                rankData.sort == sortNo?.toInt()
            }
            
            if (myRankData != null) {
                // 用户在榜上，显示榜上数据
                val userModel = myRankData.userModel
                val broadcasterModel = myRankData.broadcastetModel
                
                if (userModel != null && broadcasterModel != null ) {
                    // 情侣模式：显示两个头像和组合昵称
                    val nickname1 = CustomUtils.rankTruncateNickname(userModel.nickname ?: "Unknown",15)
                    val nickname2 = CustomUtils.rankTruncateNickname(broadcasterModel.nickname ?: "Unknown",15)
                    binding.tvMineName.text = "$nickname1 & $nickname2"
                    
                    // 显示主播头像
                    loadAvatar(broadcasterModel, binding.tvCoupleAvatar2)
                    binding.tvCoupleAvatar2.visibility = VISIBLE
                    
                    binding.tvCoupleAvatar2.click {
                        navigateToBroadcasterDetail(broadcasterModel)
                    }
                    
                }
            }
            
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "更新个人排名信息显示失败")
        }
    }
    
}
