package com.score.callmetest.ui.widget

import android.app.Dialog
import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.score.callmetest.R
import com.score.callmetest.databinding.PaymentSuccessDialogBinding
import com.score.callmetest.util.CustomUtils
import com.score.callmetest.util.click

/**
 * 支付成功弹窗
 * @param context 上下文
 * @param coinText 金币文本
 * @param onConfirm 确认回调
 */
class PaymentSuccessDialog(
    context: Context,
    private val coinText: String,
    private val onConfirm: (() -> Unit)? = null
) : Dialog(context, androidx.appcompat.R.style.Theme_AppCompat_Dialog) {

    private lateinit var binding: PaymentSuccessDialogBinding


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 加载布局
        binding = PaymentSuccessDialogBinding.inflate(LayoutInflater.from(context))
        setContentView(binding.root)

        // 设置弹窗属性
        setCancelable(false)
        setCanceledOnTouchOutside(false)

        // 设置窗口属性，居中显示，左右边距37dp
        window?.setLayout(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.WRAP_CONTENT
        )

        // 隐藏状态栏
        window?.decorView?.systemUiVisibility = (
                View.SYSTEM_UI_FLAG_FULLSCREEN or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                )
        window?.setBackgroundDrawableResource(R.color.transparent)

        // 设置左右37dp边距
        val paddingPx = (37 * context.resources.displayMetrics.density).toInt()
        binding.root.setPadding(
            paddingPx,
            binding.root.paddingTop,
            paddingPx,
            binding.root.paddingBottom
        )

        initViews()
        setupClickListeners()
        playSuccessAnimation()
    }

    private fun initViews() {
        // 通过ViewBinding直接设置金币文本
        binding.dialogCoinText.text = "+$coinText"
    }

    private fun setupClickListeners() {
        binding.btnOkLayout.click {
            onConfirm?.invoke()
            dismiss()
        }
    }

    private fun playSuccessAnimation() {
        // 通过ViewBinding直接播放SVGA动画
        CustomUtils.playSvgaOnce(binding.svgaCoin, "pay_success.svga")
    }

    companion object {
        /**
         * 显示支付成功弹窗
         * @param context 上下文
         * @param coinText 金币文本
         * @param onConfirm 确认回调
         * @return 弹窗实例
         */
        fun show(
            context: Context,
            coinText: String,
            onConfirm: (() -> Unit)? = null
        ): PaymentSuccessDialog {
            val dialog = PaymentSuccessDialog(context, coinText, onConfirm)
            dialog.show()
            return dialog
        }
    }
}
