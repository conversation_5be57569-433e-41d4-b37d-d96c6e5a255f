package com.score.callmetest.ui.home.ranklist

import android.view.LayoutInflater
import android.view.View
import android.view.View.GONE
import android.view.View.OVER_SCROLL_NEVER
import android.view.View.VISIBLE
import android.view.ViewGroup
import androidx.core.graphics.toColorInt
import androidx.core.view.doOnPreDraw
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.score.callmetest.R
import com.score.callmetest.databinding.FragmentRichListBinding
import com.score.callmetest.manager.UserInfoManager
import com.score.callmetest.network.UserRankModel
import com.score.callmetest.ui.base.BaseFragment
import com.score.callmetest.ui.home.ranklist.adapter.RichListAdapter
import com.score.callmetest.util.CustomUtils
import com.score.callmetest.util.DisplayUtils
import com.score.callmetest.util.DrawableUtils
import com.score.callmetest.util.GlideUtils
import timber.log.Timber

/**
 * 用户榜Fragment
 * 显示用户排行榜数据，包括前三名展示和列表
 */
class RichListFragment : BaseFragment<FragmentRichListBinding, RankListViewModel>() {
    private val TAG = "RichListFragment"

    private lateinit var mAdapter: RichListAdapter
    private var mEmptyView: View? = null

    override fun getViewBinding(
        inflater: LayoutInflater,
        container: ViewGroup?
    ): FragmentRichListBinding {
        return FragmentRichListBinding.inflate(inflater, container, false)
    }

    override fun getViewModelClass(): Class<RankListViewModel> {
        return RankListViewModel::class.java
    }

    override fun initView() {
        super.initView()

        // 获取Activity的ViewModel实例，确保数据共享
        viewModel = ViewModelProvider(requireActivity())[RankListViewModel::class.java]

        setupRecyclerView()
        setupPersonalRankInfo()
        // 设置时间背景
        binding.lineRichTime.background = DrawableUtils.createRoundRectDrawableWithStroke(
            fillColor = "#30ffffff".toColorInt(),
            radius = DisplayUtils.dp2pxInternalFloat(19f),
            strokeColor = "#4affffff".toColorInt(),
            strokeWidth = DisplayUtils.dp2pxInternal(1f)
        )

        binding.mainContentLayout.doOnPreDraw {
            initMainContentLayout()
            setupRankTopAnimation()
        }
    }

    private fun initMainContentLayout() {
        val screenHeight = binding.rootView.height
        // 限制BottomSheet最大高度为屏幕高度-250dp
        val maxHeight = screenHeight - DisplayUtils.dp2px(250f)

        val params = binding.mainContentLayout.layoutParams
        params.height = maxHeight
        binding.mainContentLayout.layoutParams = params

        val behavior = BottomSheetBehavior.from(binding.mainContentLayout)
        // 设置初始高度为屏幕的55%
        behavior.peekHeight = screenHeight * 55 / 100
        behavior.state = BottomSheetBehavior.STATE_COLLAPSED
    }

    override fun initData() {
        super.initData()

        // 观察排行榜数据
        viewModel.userRankData.observe(this) { rankResponse ->
            rankResponse?.let { data ->

                // 更新月份显示
                data.monthName?.let { monthName ->
                    binding.tvTime.text = monthName
                }

                // 更新排行榜列表数据
                data.rankData?.let { rankList ->
                    // 检查数据是否为空
                    if (rankList.isEmpty()) {
                        updateEmptyViewVisibility(true)
                    } else {
                        updateEmptyViewVisibility(false)
                        // 从第4名开始展示
                        mAdapter.submitRankList(rankList)
                        // 更新前三名显示
                        updateTopThreeDisplay(rankList)
                    }
                } ?: run {
                    // rankData为null时显示空页面
                    updateEmptyViewVisibility(true)
                }

                // 更新个人排名
                data.sortNo?.let { sortNo ->
                    binding.tvMineRank.text = sortNo.ifBlank { "50+" }
                }
            }
        }

        // 观察错误信息
        viewModel.errorMessage.observe(this) { errorMessage ->
            errorMessage?.let { error ->
                if (error.second == 0) {
                    Timber.tag(TAG).e("排行榜数据加载错误 ${error.first}")
                    // 错误时显示空页面
                    updateEmptyViewVisibility(true)
                }

            }
        }

    }

    /**
     * 设置RecyclerView
     */
    private fun setupRecyclerView() {
        mAdapter = RichListAdapter()

        binding.rvRich.apply {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = mAdapter
            // 禁用过度滚动效果
            overScrollMode = OVER_SCROLL_NEVER
        }
    }

    /**
     * 设置rank_top的Svga动画
     */
    private fun setupRankTopAnimation() {
        // 播放rank_top.svga动画
        CustomUtils.playSvga(binding.rankTop, "rank_top.svga")
    }
    /**
     * 设置个人排名信息
     */
    private fun setupPersonalRankInfo() {
        val myUserInfo = UserInfoManager.myUserInfo
        myUserInfo?.let { userInfo ->
            // 显示个人头像
            userInfo.avatarThumbUrl?.let { avatarUrl ->
                GlideUtils.load(
                    binding.tvMineAvatar,
                    avatarUrl,
                    placeholder = R.drawable.placeholder,
                    error = R.drawable.placeholder,
                    isCircle = true
                )
            } ?: run {
                // 没有头像时显示默认头像
                binding.tvMineAvatar.setImageResource(R.drawable.placeholder)
            }
            // 显示个人昵称
            binding.tvMineName.text = CustomUtils.rankTruncateNickname(userInfo.nickname ?: "Unknown",30)
        } ?: run {
            Timber.tag(TAG).w("未获取到用户信息")
            binding.tvMineName.text = "Unknown"
            binding.tvMineAvatar.setImageResource(R.drawable.placeholder)
        }
    }

    /**
     * 更新前三名显示
     */
    private fun updateTopThreeDisplay(rankList: List<UserRankModel>) {
        try {
            // 第一名
            if (rankList.isNotEmpty()) {
                val firstUser = rankList[0]
                binding.tvAvatar1Name.text = CustomUtils.rankTruncateNickname(firstUser.nickname ?: "Unknown",20)
                loadAvatar(firstUser, binding.ivAvatar1)
            }

            // 第二名
            if (rankList.size > 1) {
                val secondUser = rankList[1]
                binding.tvAvatar2Name.text = CustomUtils.rankTruncateNickname(secondUser.nickname ?: "Unknown",15)
                loadAvatar(secondUser, binding.ivAvatar2)
            }

            // 第三名
            if (rankList.size > 2) {
                val thirdUser = rankList[2]
                binding.tvAvatar3Name.text = CustomUtils.rankTruncateNickname(thirdUser.nickname ?: "Unknown",15)
                loadAvatar(thirdUser, binding.ivAvatar3)
            }
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "更新前三名显示时出错")
        }
    }

    /**
     * 统一头像加载方法
     */
    private fun loadAvatar(userRank: UserRankModel, imageView: android.widget.ImageView) {
        // 优先使用avatar，其次使用avatarMapPath，最后使用默认头像
        val avatarUrl = userRank.avatar ?: userRank.avatarMapPath

        if (!avatarUrl.isNullOrBlank()) {
            GlideUtils.load(
                imageView,
                avatarUrl,
                placeholder = R.drawable.placeholder,
                error = R.drawable.placeholder,
                isCircle = true
            )
        } else {
            imageView.setImageResource(R.drawable.placeholder)
        }
    }

    /**
     * 更新空页面的可见性
     * @param show true: 显示空页面, false: 隐藏空页面
     */
    private fun updateEmptyViewVisibility(show: Boolean) {
        try {
            if (show) {
                // 确保空状态视图已经被inflate
                if (mEmptyView == null) {
                    mEmptyView = binding.emptyView.inflate()
                }
                // 显示空状态视图，并隐藏列表
                mEmptyView?.visibility = VISIBLE
                binding.rvRich.visibility = GONE
            } else {
                // 隐藏空状态视图，并显示列表
                mEmptyView?.visibility = GONE
                binding.rvRich.visibility = VISIBLE
            }
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "更新空页面可见性失败")
        }
    }
}