package com.score.callmetest.ui.widget.checkin

import android.content.DialogInterface
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.DialogFragment
import com.score.callmetest.R
import com.score.callmetest.databinding.DialogCheckInBinding
import com.score.callmetest.manager.CheckInManager
import com.score.callmetest.util.ClickUtils
import com.score.callmetest.util.CustomUtils
import com.score.callmetest.util.DeviceUtils
import com.score.callmetest.util.DisplayUtils
import timber.log.Timber
import androidx.core.graphics.drawable.toDrawable
import androidx.core.view.isVisible
import androidx.lifecycle.lifecycleScope
import com.score.callmetest.entity.CustomEvents
import com.score.callmetest.entity.RechargeSource
import com.score.callmetest.manager.UserInfoManager
import com.score.callmetest.manager.VipManager
import com.score.callmetest.ui.mine.vip.VipActivity
import com.score.callmetest.ui.widget.SignInDialog
import com.score.callmetest.util.ActivityUtils
import com.score.callmetest.util.EventBus
import com.score.callmetest.util.ToastUtils

/**
 * 签到弹窗
 */
class CheckInDialog : DialogFragment() {

    private var _binding: DialogCheckInBinding? = null
    private val binding get() = _binding!!

    private var mIsTopUp = true

    private lateinit var checkInDayAdapter: CheckInDayAdapter

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = DialogCheckInBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        // 设置透明背景
        dialog?.window?.setBackgroundDrawable(Color.TRANSPARENT.toDrawable())
        
        initViews()
        initListeners()
        updateUI()
        EventBus.observe(lifecycleScope, CustomEvents.CheckInDialogUpdate::class.java) {
            updateUI()
        }
    }

    private fun initViews() {
        // 设置弹窗样式
        dialog?.setCancelable(true)
        dialog?.setCanceledOnTouchOutside(true)
        // 初始化RecyclerView
        setupRecyclerView()
    }

    private fun setupRecyclerView() {
        checkInDayAdapter = CheckInDayAdapter()
        
        // 使用自定义的GridLayoutManager，确保最后一个Item填满剩余宽度
        val layoutManager = CheckInGridLayoutManager(requireContext(), 4) // 第一行4个Item
        
        // 添加Item间距装饰器
        val spacing = DisplayUtils.dp2px(2f) // 2dp间距
        val itemDecoration = CheckInItemDecoration(spacing, 4)
        
        binding.rvCheckInDays.apply {
            this.layoutManager = layoutManager
            adapter = checkInDayAdapter
            addItemDecoration(itemDecoration)
        }
    }

    private fun initListeners() {
        // 关闭按钮
        ClickUtils.setOnIsolatedClickListener(binding.btnClose) {
            dismiss()
        }

        // 签到按钮
        ClickUtils.setOnIsolatedClickListener(binding.btnCheckIn) {
            if(mIsTopUp){
                // 打开签到充值界面
                SignInDialog.newInstance(
                    contentText = getString(R.string.check_in_recharge_tip),
                    source = RechargeSource.CHECK_IN,
                ).show(parentFragmentManager, "sign_in_dialog")
                return@setOnIsolatedClickListener
            }
            // 签到
            performCheckIn()
        }

        // VIP特权按钮
        ClickUtils.setOnIsolatedClickListener(binding.vipPrivilegeLayout) {
            onVipPrivilegeClicked()
        }
    }

    private fun updateUI() {
        val continuousDays = CheckInManager.getContinuousCheckInDays()
        val isCheckedToday = CheckInManager.isCheckedInToday()

        // 更新RecyclerView中的签到状态
        checkInDayAdapter.updateCheckInStatus(continuousDays, isCheckedToday)

        // 检查是否是vip
        val isVip = VipManager.isVip()
        if(isVip){
            binding.vipPrivilegeLayout.isVisible = false
        }

        // 签到按钮动画清除
        try {
            binding.btnCheckIn.stopAnimation()
            binding.btnCheckIn.clear()
        } catch (e: Exception) { }
        // 更新签到按钮状态
        if (isCheckedToday) {
            // 当天已签到
            binding.btnCheckIn.isVisible = false
            binding.btnCheckedIn.isVisible = true
        } else {
            // 当天未签到
            mIsTopUp = UserInfoManager.myUserInfo?.let { userInfo ->
                if(userInfo.isRecharge && CheckInManager.hasRechargedInPaidUserCheckInDelay()){
                    // 付费用户 && 付费用户每120小时(不含120小时)内充值过
                    return@let false
                }
                if(!userInfo.isRecharge && CheckInManager.hasRechargedInFreeUserCheckInDelay()){
                    // 免费用户 && 免费用户每48小时(不含48小时)内充值过
                    return@let false
                }
                return@let true
            }?: true
            // test
//            mIsTopUp = false
            val svgaName = if (mIsTopUp) "btn_top_up.svga" else "btn_check_in.svga"
            CustomUtils.playSvgaOnce(binding.btnCheckIn, svgaName)
            binding.btnCheckIn.isVisible = true
            binding.btnCheckedIn.isVisible = false
        }

        Timber.d("CheckInDialog: 更新UI - 连续签到${continuousDays}天, 今日已签到:${isCheckedToday}")
    }



    private fun performCheckIn() {
        if (CheckInManager.isCheckedInToday()) {
            Timber.d("今天已经签到过了")
            return
        }

        CheckInManager.checkIn(lifecycleScope,
            onSuccess = {
                ToastUtils.showShortToast(requireContext().getString(R.string.check_in_success))
                dismiss()
                onCheckInSuccess()
            },
            onError = { error ->
                ToastUtils.showShortToast(error)
            }
        )
    }

    override fun onDismiss(dialog: DialogInterface) {
        CheckInManager.dismissCheckInDialog()
        super.onDismiss(dialog)
    }

    /**
     * 签到成功回调
     * 可以在这里处理签到奖励等逻辑
     */
    private fun onCheckInSuccess() {
        val continuousDays = CheckInManager.getContinuousCheckInDays()
        val rewards = CheckInManager.getCheckInRewards()

        if (rewards != null && continuousDays >= 0) {
            val todayReward = rewards[continuousDays % rewards.size]
            Timber.d("CheckInDialog: 获得签到奖励 - ${todayReward.amount}, type=${todayReward.rewardType}")

            // 显示签到成功奖励弹窗
            activity?.let { act ->
                CheckInManager.showCheckInSuccessDialog(act as AppCompatActivity, todayReward)
            }

            //用户信息更新
            UserInfoManager.refreshMyUserInfo()
        } else {
            Timber.w("CheckInDialog: 无法获取签到奖励数据")
        }
    }

    /**
     * VIP特权点击事件
     * 预留接口，可以跳转到VIP购买页面
     */
    private fun onVipPrivilegeClicked() {
        Timber.d("CheckInDialog: 点击VIP特权按钮")
        gotoVipPage()
    }

    private fun gotoVipPage() {
        ActivityUtils.getTopActivity()?.let {
            ActivityUtils.startActivity(it, VipActivity::class.java)
            dismiss()
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        try {
            binding.btnCheckIn.stopAnimation()
            binding.btnCheckIn.clear()
        } catch (e: Exception) {
        }
        _binding = null
    }

    override fun onStart() {
        super.onStart()
        
        // 设置弹窗大小
        dialog?.window?.setLayout(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.WRAP_CONTENT
        )
    }
}
