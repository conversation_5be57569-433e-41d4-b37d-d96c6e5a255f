package com.score.callmetest.ui.main

import android.view.View
import androidx.core.view.isVisible
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.GridLayoutManager
import com.score.callmetest.Constant
import com.score.callmetest.R
import com.score.callmetest.databinding.ActivityCoinStoreBinding
import com.score.callmetest.entity.RechargeSource
import com.score.callmetest.manager.CountdownManager
import com.score.callmetest.manager.GlobalManager
import com.score.callmetest.manager.GoodsManager
import com.score.callmetest.manager.GoodsManager.VIP_CODE_1
import com.score.callmetest.manager.PaymentMethodManager
import com.score.callmetest.manager.RechargeManager
import com.score.callmetest.manager.StrategyManager
import com.score.callmetest.manager.UserInfoEvent
import com.score.callmetest.manager.UserInfoManager
import com.score.callmetest.manager.VipManager
import com.score.callmetest.network.GoodsInfo
import com.score.callmetest.ui.base.BaseActivity
import com.score.callmetest.ui.chat.ChatActivity
import com.score.callmetest.ui.main.RechargeOptionAdapter.Companion.createWithoutLimit
import com.score.callmetest.ui.widget.GridSpacingItemDecoration
import com.score.callmetest.util.CustomUtils
import com.score.callmetest.util.DisplayUtils
import com.score.callmetest.util.EventBus
import com.score.callmetest.util.ToastUtils
import com.score.callmetest.util.click
import kotlinx.coroutines.launch

/**
 * 金币充值页面
 */
class CoinStoreActivity : BaseActivity<ActivityCoinStoreBinding, CoinStoreViewModel>() {
    private var adapter: RechargeOptionAdapter? = null

    override fun getViewBinding() = ActivityCoinStoreBinding.inflate(layoutInflater)
    override fun getViewModelClass() = CoinStoreViewModel::class.java

    override fun initView() {
        GlobalManager.addViewStatusBarTopMargin(this, binding.llTopBar)

        binding.tvTitle.text = getString(R.string.coinStore)
        // 返回按钮点击关闭页面
        binding.ivBack.click {
            finish()
        }
        // 强制刷新当前用户信息
        lifecycleScope.launch {
            val userInfo = UserInfoManager.myUserInfo?.userId?.let {
                UserInfoManager.fetchUserInfo(it)
            }
            if (userInfo != null) {
                UserInfoManager.updateMyUserInfo(userInfo)
            }
            // 显示当前金币数
            val coinCount = UserInfoManager.myUserInfo?.availableCoins ?: 0
            binding.tvCoinBalance.text = "$coinCount"
        }
        // 显示当前金币数
        showCoins()

       /* binding.tvMyCoins.text = CustomUtils.createCoinSpannableText(
            context = this,
            text = "My coins: icon $coinCount",
            coinSizeDp = 18f,
            alignment = ImageSpan.ALIGN_BOTTOM,
            spacesBefore = 0,
            spacesAfter = 0)*/

        binding.rvRechargeOptions.layoutManager = GridLayoutManager(this, 2)
        // 添加卡片间距，13dp
        val spacing = DisplayUtils.dp2px(13f)
        binding.rvRechargeOptions.addItemDecoration(GridSpacingItemDecoration(2, spacing, true))

        if (StrategyManager.isReviewPkg()) {
            binding.btnCustomerService.visibility = View.GONE
        }
        binding.btnCustomerService.click {
            ChatActivity.start(this@CoinStoreActivity, CustomUtils.provideCustomService())
        }

        binding.llTopRow.isVisible = false
        binding.llRightColumn.isVisible = false

    }

    private fun showCoins() {
        val coinCount = UserInfoManager.myUserInfo?.availableCoins ?: 0
        binding.tvCoinBalance.text = "$coinCount"
    }

    override fun initData() {
        super.initData()
        // 预先加载支付渠道列表
        lifecycleScope.launch {
            PaymentMethodManager.ensurePayChannelListLoaded()
        }
        updateGoodsUI()
    }

    override fun initListener() {
        EventBus.observe(this, UserInfoEvent::class.java) { event ->
            showCoins()
        }
    }

    private fun updateGoodsUI() {
        val cachedGoods = GoodsManager.getCachedAllGoods()
        if (cachedGoods.isNotEmpty()) {
            updateGoodsListToUI(cachedGoods)
        } else {
            lifecycleScope.launch {
                try {
                    val goodsList = GoodsManager.getAllGoods()
                    updateGoodsListToUI(goodsList)
                } catch (e: Exception) {
                    ToastUtils.showShortToast(getString(R.string.net_error_and_try_again))
                }
            }
        }
    }

    private fun updateGoodsListToUI(goodsList: List<GoodsInfo>) {
        // 先初始化倒计时状态（使用统一的商品倒计时管理）
        goodsList.forEach { goods ->
            if ((goods.type == "1" || goods.type == "2") && !goods.code.isNullOrEmpty()) {
                val remainTime = goods.remainMilliseconds ?: goods.surplusMillisecond
                if (remainTime != null && remainTime > 0) {
                    CountdownManager.initOrUpdateCountdown(
                        goods.code,
                        goods.type,
                        remainTime
                    )
                }
            }
        }

        val rechargeOptions = goodsList
            .map { goods ->
                val (priceSymbol, price) = GoodsManager.getLocaleGoodsPrice(goods)
                val (oldPriceSymbol, oldPrice) = GoodsManager.getLocaleGoodsPrice(goods,true)
                // 使用缓存的支付渠道列表，避免在map中调用suspend函数
                val payChannelList = PaymentMethodManager.getCachedPayChannelList()
                var discount = 0
                if (!payChannelList.isNullOrEmpty()) {
                    payChannelList.forEachIndexed { index, item ->
                        val calculatedDiscount = CustomUtils.calculateChannelDiscount(goods.type, goods.thirdpartyCoinPercent, item)
                        if (calculatedDiscount > discount) {
                            discount = calculatedDiscount
                        }
                    }
                }
                RechargeOption(
                    iconRes = GoodsManager.getIconByGoodsId(goods.code),
                    coinAmount = (goods.exchangeCoin ?: 0) ,
                    price = price?.let { "$priceSymbol$it"}?:"",
                    oldPrice = oldPrice?.let { oldPriceSymbol+it }?:"",
                    priceSymbol = priceSymbol,
                    name = goods.name,
                    tags = goods.tags,
                    extraCoinPercent = goods.extraCoinPercent,
                    discount =  discount,
                    goodsCode = goods.code,
                    type = goods.type,
                    remainMilliseconds = goods.remainMilliseconds ?: goods.surplusMillisecond,
                    invitationId = goods.invitationId
                )
            }
        val distinctOptions = rechargeOptions
            // 保留了类型为 "0" 的商品，或者类型为 "1" 和“2”且剩余时间大于 0 的商品。
            .filter { it.type == "0" || ((it.remainMilliseconds ?: 0L) > 0L) }

        adapter = createWithoutLimit(
            options = distinctOptions,
            onClick = { option -> handleRechargeOptionClick(option) },
            onCountdownEnd = { updateGoodsUI() } // 添加计时结束回调
        )
        binding.rvRechargeOptions.adapter = adapter
    }

    private fun handleRechargeOptionClick(option: RechargeOption) {
        option.goodsCode?.let { goodsCode ->
            if (GoodsManager.isVipGoods(goodsCode)) {
                VipManager.showVipDialog()
            } else {
                RechargeManager.startRecharge(
                    activity = this@CoinStoreActivity,
                    goodsCode = goodsCode,
                    entry = RechargeSource.SUBSCRIBE_DETAIL
                )
            }
        }
    }

    private fun isNativePay(channel: com.score.callmetest.network.PayChannelItem): Boolean {
        return channel.payChannel == PaymentMethodManager.PAY_CHANNEL_GP
    }

    override fun onDestroy() {
        // 清理 Adapter 中的倒计时监听者
        adapter?.clearAllListeners()

        // 清理 RecyclerView 和 Adapter
        binding.rvRechargeOptions.adapter = null
        adapter = null
        super.onDestroy()
    }
}