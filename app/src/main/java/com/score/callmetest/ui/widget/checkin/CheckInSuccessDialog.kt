package com.score.callmetest.ui.widget.checkin

import android.graphics.Color
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.graphics.drawable.toDrawable
import androidx.core.os.BundleCompat
import androidx.core.view.doOnPreDraw
import androidx.fragment.app.DialogFragment
import com.score.callmetest.R
import com.score.callmetest.databinding.DialogCheckInSuccessBinding
import com.score.callmetest.manager.CheckInManager
import com.score.callmetest.manager.GlobalManager
import com.score.callmetest.network.CheckinRewardResp
import com.score.callmetest.util.ClickUtils
import com.score.callmetest.util.CustomUtils
import com.score.callmetest.util.DrawableUtils
import com.score.callmetest.util.GlideUtils
import timber.log.Timber

/**
 * 签到成功奖励弹窗
 * 根据奖励类型显示不同的UI效果
 */
class CheckInSuccessDialog : DialogFragment() {

    companion object {
        private const val TAG = "CheckInSuccessDialog"
        private const val ARG_REWARD_DATA = "reward_data"

        /**
         * 创建签到成功弹窗实例
         * @param reward 奖励数据
         */
        fun newInstance(reward: CheckinRewardResp): CheckInSuccessDialog {
            return CheckInSuccessDialog().apply {
                arguments = Bundle().apply {
                    putParcelable(ARG_REWARD_DATA, reward)
                }
            }
        }
    }

    private var _binding: DialogCheckInSuccessBinding? = null
    private val binding get() = _binding!!

    private var reward: CheckinRewardResp? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            reward = BundleCompat.getParcelable(it,ARG_REWARD_DATA, CheckinRewardResp::class.java)
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = DialogCheckInSuccessBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        // 设置透明背景
        dialog?.window?.setBackgroundDrawable(Color.TRANSPARENT.toDrawable())
        
        initViews()
        initListeners()
        setupRewardDisplay()
    }

    private fun initViews() {
        // 设置弹窗样式
        dialog?.setCancelable(true)
        dialog?.setCanceledOnTouchOutside(true)

        binding.btnOk.doOnPreDraw {
            binding.btnOk.background = DrawableUtils.createGradientDrawable(
                colors = GlobalManager.getMainButtonBgGradientColors(),
                radius = binding.btnOk.height / 2f
            )
        }
    }

    private fun initListeners() {
        // OK按钮点击事件
        ClickUtils.setOnIsolatedClickListener(binding.btnOk) {
            dismiss()
        }
    }

    /**
     * 根据奖励数据设置显示内容
     */
    private fun setupRewardDisplay() {
        val rewardData = reward ?: return
        binding.apply {
            // 设置奖励数量文本
            tvRewardAmount.text = rewardData.amount.toString()

            GlideUtils.load(requireContext(), rewardData.iconUrl, ivRewardIcon)
        }
    }

    override fun onStart() {
        super.onStart()
        
        // 设置弹窗大小
        dialog?.window?.setLayout(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.WRAP_CONTENT
        )
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
