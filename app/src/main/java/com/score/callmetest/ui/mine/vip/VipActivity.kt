package com.score.callmetest.ui.mine.vip

import android.graphics.Color
import android.graphics.drawable.GradientDrawable
import android.os.Build
import android.os.Bundle
import android.text.SpannableString
import android.view.View
import androidx.core.graphics.toColorInt
import androidx.lifecycle.lifecycleScope
import com.score.callmetest.BuildConfig
import com.score.callmetest.CallmeApplication
import com.score.callmetest.R
import com.score.callmetest.databinding.ActivityAboutUsBinding
import com.score.callmetest.databinding.ActivityVipBinding
import com.score.callmetest.entity.RechargeSource
import com.score.callmetest.manager.CheckInManager
import com.score.callmetest.manager.DualChannelEventManager
import com.score.callmetest.manager.GlobalManager
import com.score.callmetest.manager.GoodsManager
import com.score.callmetest.manager.RatingManager
import com.score.callmetest.manager.RechargeManager
import com.score.callmetest.manager.UserInfoManager
import com.score.callmetest.manager.VipManager
import com.score.callmetest.network.GoodsInfo
import com.score.callmetest.ui.base.BaseActivity
import com.score.callmetest.ui.base.EmptyViewModel
import com.score.callmetest.ui.rating.RatingDialog
import com.score.callmetest.ui.web.WebViewActivity
import com.score.callmetest.ui.widget.PromotionDialogFragment
import com.score.callmetest.ui.widget.VipItemCardView
import com.score.callmetest.ui.widget.VipItemCardView.OnCardClickListener
import com.score.callmetest.util.ActivityUtils
import com.score.callmetest.util.DrawableUtils
import com.score.callmetest.util.EventBus
import com.score.callmetest.util.GlideUtils
import com.score.callmetest.util.click
import kotlinx.coroutines.launch

import timber.log.Timber

class VipActivity : BaseActivity<ActivityVipBinding, EmptyViewModel>() {

    private var selectedItem: VipItemCardView? = null

    override fun getViewBinding(): ActivityVipBinding {
        return ActivityVipBinding.inflate(layoutInflater)
    }

    override fun getViewModelClass() = EmptyViewModel::class.java

    override fun initView() {
        GlobalManager.addViewStatusBarTopMargin(this, binding.toolbar)

        // 初始化用户信息
        setupUserInfo()

        // 初始化VIP订阅卡片
        setupVipCards()

        refreshData()

        // 高亮显示指定文本
        val spannableString = SpannableString("Become VIP to unlock 7 privileges")
        val highlightText = "7"
        val highlightColor = "#FFFF4444".toColorInt()
        val startIndex = spannableString.indexOf(highlightText)
        if (startIndex != -1) {
            spannableString.setSpan(
                android.text.style.ForegroundColorSpan(highlightColor),
                startIndex,
                startIndex + highlightText.length,
                android.text.Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
            )
        }
        binding.tvVipPrivilegesTitle.text = spannableString

        binding.bottomShadow.background = DrawableUtils.createGradientDrawable(
            colors = intArrayOf("#00FFFFFF".toColorInt(), Color.WHITE, Color.WHITE),
            orientation = GradientDrawable.Orientation.TOP_BOTTOM
        )
    }

    override fun initListener() {
        // 返回按钮点击事件
        binding.ivBack.click {
            Timber.tag("dsc--VipActivity").d("返回按钮点击")
            finish()
        }

        binding.vipButton.click {
            if (selectedItem != null) {
                ActivityUtils.getTopActivity()?.let {
                    RechargeManager.startRecharge(
                        activity = it,
                        goodsCode = selectedItem!!.getData()!!.code.toString(),
                        entry = RechargeSource.SUBSCRIBE_DIALOG,
                    )
                }
            }
        }

        // 监听三方支付回调
        DualChannelEventManager.observeRechargeOrderStatus(this) {
            if (it.orderNo == null) return@observeRechargeOrderStatus
            when (it.status) {
                2 -> {
                    setupUserInfo(true)
                    // 更新商品列表
                    lifecycleScope.launch {
                        GoodsManager.refreshAllGoods()
                    }
                }

                3 -> {
                    // 充值失败
                }
            }
        }

        EventBus.observe(lifecycleScope, RechargeManager.GPSuccessEvent::class.java) { event ->
            setupUserInfo(true)
        }
    }

    /**
     * 设置用户信息
     */
    private fun setupUserInfo(forceUpdate: Boolean = false) {
        UserInfoManager.getUserInfo(
            userId = UserInfoManager.myUserInfo?.userId,
            forceUpdate = forceUpdate,
        ) { currentUser ->
            if (currentUser != null) {
                if (forceUpdate) {
                    UserInfoManager.updateMyUserInfo(currentUser)
                }

                binding.tvUserName.text = currentUser.nickname ?: "User"

                // 设置用户头像
                GlideUtils.load(
                    binding.ivUserAvatar,
                    currentUser.avatarUrl,
                    placeholder = R.drawable.placeholder
                )
                VipManager.getEquity(
                    callback = {
                        // 设置VIP状态
                        if (VipManager.isVip() && VipManager.getExpiredTime() != null) {
                            try {
                                val t = getString(
                                    R.string.expiry_date_text,
                                    DateUtils.getDate(VipManager.getExpiredTime()!!.toLong())
                                )
                                binding.tvVipStatus.text = t
                            } catch (e: Exception) {
                                binding.tvVipStatus.text = getString(R.string.you_are_vip)
                            }
                            binding.ivBadge.setImageResource(R.drawable.vip_badge)
                        } else {
                            binding.tvVipStatus.text = getString(R.string.you_are_not_vip)
                            binding.ivBadge.setImageResource(R.drawable.normal_badge)
                        }
                    }
                )
            }
        }
    }

    /**
     * 设置VIP订阅卡片
     */
    private fun setupVipCards() {
        val list = GoodsManager.getCachedVipGoods()
        selectedItem = binding.vipCard1month
        if (list.size > 0) {
            binding.vipCard1month.setData(
                GoodsManager.getCachedVipGoods()[0],
                R.drawable.vip_item_icon_1,
                style = VipItemCardView.Style.DARK
            ).setOnCardClickListener(object : OnCardClickListener {
                override fun onCardClick(data: GoodsInfo?) {
                    selectedItem = binding.vipCard1month
                    refreshData()
                }
            })
        } else {
            binding.vipCard1month.visibility = View.GONE
            binding.vipCard3month.visibility = View.GONE
            binding.vipCard12month.visibility = View.GONE
        }

        if (list.size > 1) {
            binding.vipCard3month.setData(
                GoodsManager.getCachedVipGoods()[1],
                R.drawable.vip_item_icon_2
            ).setOnCardClickListener(object : OnCardClickListener {
                override fun onCardClick(data: GoodsInfo?) {
                    selectedItem = binding.vipCard3month
                    refreshData()
                }
            })
        } else {
            binding.vipCard3month.visibility = View.GONE
            binding.vipCard12month.visibility = View.GONE
        }

        if (list.size > 2) {
            binding.vipCard12month.setData(
                GoodsManager.getCachedVipGoods()[2],
                R.drawable.vip_item_icon_3
            ).setOnCardClickListener(object : OnCardClickListener {
                override fun onCardClick(data: GoodsInfo?) {
                    selectedItem = binding.vipCard12month
                    refreshData()
                }
            })
        } else {
            binding.vipCard12month.visibility = View.GONE
        }
    }

    private fun refreshData() {
        binding.vipCard1month.updateUI(VipItemCardView.Style.LIGHT)
        binding.vipCard3month.updateUI(VipItemCardView.Style.LIGHT)
        binding.vipCard12month.updateUI(VipItemCardView.Style.LIGHT)
        selectedItem?.updateUI(VipItemCardView.Style.DARK)
        // 设置VIP特权描述项数据
        setupVipPrivileges()
    }

    /**
     * 设置VIP特权描述项数据
     */
    private fun setupVipPrivileges() {
        // 1. 金币奖励
        binding.vipDescCoinsBonus.setDataWithHighlight(
            R.drawable.vip_item_1,
            "Coins bonus",
            "Get ${selectedItem?.getData()?.exchangeCoin} Coins after activating",
            "${selectedItem?.getData()?.exchangeCoin}"
        )

        // 2. 充值奖励
        binding.vipDescRechargeBonus.setDataWithHighlight(
            R.drawable.vip_item_2,
            "Recharge bonus",
            "Up to 20% extra coins for each recharge",
            "20%"
        )

        // 3. 通话折扣
        binding.vipDescCallDiscount.setDataWithHighlight(
            R.drawable.vip_item_3,
            "Call Discount",
            "10% off video calls",
            "10%"
        )

        // 4. 匹配通话优惠
        binding.vipDescMatchCall.setDataWithHighlight(
            R.drawable.vip_item_4,
            "Match Call offer",
            "10% off Match Call",
            "10%"
        )

        // 5. 免费文字聊天
        binding.vipDescTextChat.setData(
            R.drawable.vip_item_5,
            "Free Text Chat",
            "Text Chat all for free"
        )

        // 6. 查看照片相册
        binding.vipDescPhotoAlbum.setData(
            R.drawable.vip_item_6,
            "View her photo album",
            "Check all her photos for free"
        )

        // 7. 查看访客列表
        binding.vipDescVisitorList.setData(
            R.drawable.vip_item_7,
            "View Visitor list",
            "Unlock a list of girls interested in you"
        )

        // 8. 精致VIP状态
        binding.vipDescStatus.setData(
            R.drawable.vip_item_8,
            "Exquisite VIP status",
            "Distinguished status is unique"
        )

    }

} 