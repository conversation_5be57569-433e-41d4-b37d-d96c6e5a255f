package com.score.callmetest.ui.videocall

import android.app.Dialog
import android.content.DialogInterface
import android.os.Bundle
import android.text.SpannableString
import android.text.Spanned
import android.text.style.ForegroundColorSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.core.graphics.toColorInt
import androidx.lifecycle.lifecycleScope
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.score.callmetest.CallmeApplication
import com.score.callmetest.R
import com.score.callmetest.databinding.FragmentCallEndFreeBinding
import com.score.callmetest.entity.RechargeSource
import com.score.callmetest.network.UserInfo
import com.score.callmetest.util.GlideUtils
import com.score.callmetest.util.ToastUtils
import com.score.callmetest.util.click

/**
 * 通话结束后的金币弹框
 */
class CallEndFreeFragmentDialog(
    val userInfo: UserInfo,
    val onDismiss: (DialogInterface) -> Unit = {}
) : BottomSheetDialogFragment() {
    private var _binding: FragmentCallEndFreeBinding? = null
    private val binding get() = _binding!!

    // 可选：后续可注入ViewModel用于请求getCallResult
    // private lateinit var viewModel: VideoCallViewModel

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        _binding = FragmentCallEndFreeBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        // 设置弹窗全屏或自定义样式
        dialog?.window?.setLayout(
            WindowManager.LayoutParams.MATCH_PARENT,
            WindowManager.LayoutParams.MATCH_PARENT
        )
       // dialog?.window?.setBackgroundDrawable(Color.GRAY.toDrawable())

        // 返回按钮逻辑
        binding.close.click {
            dismiss()
        }

        binding.followLayout.setIsFriend(userInfo.isFriend)
        GlideUtils.load(
            imageView = binding.avatar,
            doubleUrl = GlideUtils.DoubleUrl(userInfo.avatarUrl, userInfo.avatarThumbUrl),
            placeholder = R.drawable.placeholder
        )
        refreshName()

        binding.followLayout.click {
            binding.followLayout.addFriend(lifecycleScope, userInfo.userId!!) { friend ->
                if (friend) {
                    userInfo.isFriend = true
                    ToastUtils.showToast(CallmeApplication.context.getString(R.string.follow_success))
                } else {
                    ToastUtils.showToast(CallmeApplication.context.getString(R.string.follow_failed))
                }
            }
        }

        binding.rechargeSubView.entry = RechargeSource.COIN_NOT_ENOUGH
        // 设置支付成功回调，关闭弹窗
        binding.rechargeSubView.onRechargeClick = {
            dismiss()
        }

    }

    private fun refreshName() {
        val content = "Do you like ${userInfo.nickname}? \nRecharge to call back!"
        val nameStr = userInfo.nickname ?: ""
        val start = content.indexOf(nameStr)
        val end = start + nameStr.length
        val spannable = SpannableString(content)
        if (start >= 0 && nameStr.isNotEmpty()) {
            spannable.setSpan(
                ForegroundColorSpan("#FF7F37".toColorInt()),
                start,
                end,
                Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
            )
        }
        binding.tvContent.text = spannable
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = super.onCreateDialog(savedInstanceState)
        dialog.setOnShowListener {
            val bottomSheet =
                (dialog as? BottomSheetDialog)?.findViewById<View>(com.google.android.material.R.id.design_bottom_sheet)
            bottomSheet?.setBackgroundResource(android.R.color.transparent)

            // 设置BottomSheet为完全展开状态，禁用拖拽
            val bottomSheetBehavior = BottomSheetBehavior.from(bottomSheet!!)
            bottomSheetBehavior.state = BottomSheetBehavior.STATE_EXPANDED
            bottomSheetBehavior.isDraggable = false
            bottomSheetBehavior.isHideable = false
        }
        return dialog
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        onDismiss.invoke(dialog)
        binding.followLayout.stopFollowHighlightTask()
    }
}