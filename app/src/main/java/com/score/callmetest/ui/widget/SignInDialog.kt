package com.score.callmetest.ui.widget

import android.app.Dialog
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.isVisible
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.score.callmetest.databinding.DialogSignInBinding
import com.score.callmetest.entity.RechargeSource
import com.score.callmetest.manager.DualChannelEventManager
import com.score.callmetest.manager.FlashChatManager
import com.score.callmetest.manager.StrategyManager
import com.score.callmetest.manager.UserInfoManager
import com.score.callmetest.ui.chat.ChatActivity
import com.score.callmetest.util.CustomUtils
import com.score.callmetest.util.click
// 签到弹窗和聊天限制充值弹窗，注意修改 RechargeSource
class SignInDialog(
    private val contentText: String? = null,
    private val source: RechargeSource = RechargeSource.IM_LIMIT,
    private var bcInvitationId: String? = null
) : BottomSheetDialogFragment() {
    
    private var _binding: DialogSignInBinding? = null
    private val binding get() = _binding!!

    override fun onCreateView(
        inflater: LayoutInflater, 
        container: ViewGroup?, 
        savedInstanceState: Bundle?
    ): View? {
        _binding = DialogSignInBinding.inflate(inflater, container, false)
        
        initViews()
        setupClickListeners()
        observeCoinsBalance()
        
        return binding.root
    }

    private fun initViews() {
        // 设置内容文本
        contentText?.let {
            binding.tvContentText.text = it
        }
        
        // 设置金币余额
        val coinBalance = UserInfoManager.myUserInfo?.availableCoins ?: 0
        binding.tvCoinBalance.text = " $coinBalance"

        // 如果是审核包，隐藏客服按钮
        if (StrategyManager.isReviewPkg()) {
            binding.coinService.visibility = View.GONE
        }

        // 设置充值子视图
        setupRechargeSubView()
    }

    private fun setupClickListeners() {
        // 客服按钮点击事件
        binding.coinService.click {
            ChatActivity.start(context, CustomUtils.provideCustomService())
        }
    }

    private fun setupRechargeSubView() {
        binding.rechargeSubView.bcInvitationId = bcInvitationId
        binding.rechargeSubView.entry = source
        if(!bcInvitationId.isNullOrEmpty()){
            binding.rechargeSubView.refresh{
                binding.rechargeSubView.isVisible = true
            }
        }else {
            binding.rechargeSubView.isVisible = true
        }

        // 设置支付成功回调，关闭弹窗
        binding.rechargeSubView.onRechargeClick = {
            dismiss()
        }
    }

    private fun observeCoinsBalance() {
        // 监听金币余额变化
        DualChannelEventManager.observeAvailableCoins(this) { availableCoinsMessage ->
            // 更新余额显示
            val availableCoins = availableCoinsMessage.coins ?: 0
            UserInfoManager.myUserInfo?.availableCoins = availableCoins
            binding.tvCoinBalance.text = " $availableCoins"
        }
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        FlashChatManager.stopShowDialogTimer()
        val dialog = super.onCreateDialog(savedInstanceState)
        
        dialog.setOnShowListener {
            val bottomSheet = (dialog as? BottomSheetDialog)
                ?.findViewById<View>(com.google.android.material.R.id.design_bottom_sheet)
            bottomSheet?.setBackgroundResource(android.R.color.transparent)
        }
        
        return dialog
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
        FlashChatManager.startShowDialogTimer()
    }

    companion object {
        /**
         * 创建SignInDialog实例
         * @param contentText 显示的内容文本
         * @param source 充值来源
         * @param bcInvitationId 邀请ID
         */
        fun newInstance(
            contentText: String? = null,
            source: RechargeSource = RechargeSource.IM_LIMIT,
            bcInvitationId: String? = null
        ): SignInDialog {
            return SignInDialog(contentText, source, bcInvitationId)
        }
    }
}
