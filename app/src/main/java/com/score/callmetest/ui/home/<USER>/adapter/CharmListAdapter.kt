package com.score.callmetest.ui.home.ranklist.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.graphics.toColorInt
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.score.callmetest.R
import com.score.callmetest.databinding.ItemRankBinding
import com.score.callmetest.network.BroadcasterRankModel
import com.score.callmetest.util.DisplayUtils
import com.score.callmetest.util.DrawableUtils
import com.score.callmetest.util.GlideUtils
import timber.log.Timber

/**
 * 魅力榜RecyclerView适配器
 * 负责展示排行榜列表项（从第4名开始）
 */
class CharmListAdapter : ListAdapter<BroadcasterRankModel, CharmListAdapter.RankViewHolder>(RankDiffCallback()) {
    private val TAG = "CharmListAdapter"

    private var mOnItemClickListener: ((BroadcasterRankModel) -> Unit)? = null

    private var mOnAvatarClickListener: ((BroadcasterRankModel) -> Unit)? = null

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RankViewHolder {
        val binding = ItemRankBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return RankViewHolder(binding)
    }

    override fun onBindViewHolder(holder: RankViewHolder, position: Int) {
        val item = getItem(position)
        holder.bind(item, position)
    }

    /**
     * 设置列表项点击监听器
     */
    fun setOnItemClickListener(listener: (BroadcasterRankModel) -> Unit) {
        mOnItemClickListener = listener
    }
    fun setOnAvatarClickListener(listener: (BroadcasterRankModel) -> Unit) {
        mOnAvatarClickListener = listener
    }

    /**
     * 获取实际数据列表（从第4名开始）
     */
    fun submitRankList(rankList: List<BroadcasterRankModel>) {
        // 从第4名开始，跳过前三名
        val listFromFourth = if (rankList.size > 3) {
            rankList.subList(3, rankList.size)
        } else {
            emptyList()
        }
        submitList(listFromFourth)
    }

    /**
     * ViewHolder类
     */
    inner class RankViewHolder(private val binding: ItemRankBinding) : RecyclerView.ViewHolder(binding.root) {
        
        init {
            // 设置整个item的点击事件
            itemView.setOnClickListener {
                val position = bindingAdapterPosition
                if (position != RecyclerView.NO_POSITION) {
                    val broadcasterRank = getItem(position)
                    // 先改变背景颜色
                    changeBackgroundColor()
                    mOnItemClickListener?.invoke(broadcasterRank)
                }
            }
        }
        
        /**
         * 改变背景颜色为#fff5f5fa
         */
        private fun changeBackgroundColor() {
            try {
                val backgroundColor = "#fff5f5fa".toColorInt()
                val radius = DisplayUtils.dp2pxInternalFloat(8f) // 8dp圆角
                
                itemView.background = DrawableUtils.createRoundRectDrawable(
                    color = backgroundColor,
                    radius = radius
                )
                
                // 1秒后恢复原背景
                itemView.postDelayed({
                    itemView.background = null
                }, 1000)
                
            } catch (e: Exception) {
                Timber.tag(TAG).e(e, "改变背景颜色失败")
            }
        }

        
        fun bind(broadcasterRank: BroadcasterRankModel, position: Int) {
            try {
                // 显示排名（从4开始，因为前三名单独显示）
                val rank = broadcasterRank.sort ?: (position + 4)
                binding.tvMineRank.text = rank.toString()
                
                // 统一头像加载逻辑
                loadAvatar(broadcasterRank)
                
                // 显示主播昵称
                binding.tvMineName.text = broadcasterRank.nickname ?: "Unknown"
                
            } catch (e: Exception) {
                Timber.tag(TAG).e(e, "魅力榜项绑定时出错 position=$position")
                
                // 出错时显示默认值
                binding.tvMineRank.text = (position + 4).toString()
                binding.tvMineName.text = "Unknown"
                binding.tvMineAvatar.setImageResource(R.drawable.placeholder)
            }
        }
        
        /**
         * 统一头像加载方法
         */
        private fun loadAvatar(broadcasterRank: BroadcasterRankModel) {
            // 优先使用avatar，其次使用avatarMapPath，最后使用默认头像
            val avatarUrl = broadcasterRank.avatar ?: broadcasterRank.avatarMapPath
            
            if (!avatarUrl.isNullOrBlank()) {
                GlideUtils.load(
                    binding.tvMineAvatar,
                    avatarUrl,
                    placeholder = R.drawable.placeholder,
                    error = R.drawable.placeholder,
                    isCircle = true
                )
            } else {
                binding.tvMineAvatar.setImageResource(R.drawable.placeholder)
            }
            setupAvatarClickListeners(broadcasterRank)
        }

        /**
         * 设置头像点击事件
         */
        private fun setupAvatarClickListeners(broadcasterRank: BroadcasterRankModel?) {

            // 第二个头像点击事件，主播头像
            binding.tvMineAvatar.setOnClickListener {
                broadcasterRank?.let { broadcaster ->
                    mOnAvatarClickListener?.invoke(broadcaster)
                }
            }
        }
    }


    /**
     * DiffUtil回调，用于优化列表更新性能
     */
    private class RankDiffCallback : DiffUtil.ItemCallback<BroadcasterRankModel>() {
        override fun areItemsTheSame(oldItem: BroadcasterRankModel, newItem: BroadcasterRankModel): Boolean {
            return oldItem.userId == newItem.userId
        }

        override fun areContentsTheSame(oldItem: BroadcasterRankModel, newItem: BroadcasterRankModel): Boolean {
            return oldItem == newItem
        }
    }
}
