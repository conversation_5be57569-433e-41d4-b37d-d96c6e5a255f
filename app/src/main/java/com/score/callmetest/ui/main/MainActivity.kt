package com.score.callmetest.ui.main

import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.activity.OnBackPressedCallback
import androidx.activity.viewModels
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import androidx.lifecycle.Observer
import androidx.lifecycle.lifecycleScope
import com.opensource.svgaplayer.SVGAImageView
import com.score.callmetest.CallmeApplication
import com.score.callmetest.R
import com.score.callmetest.databinding.ActivityMainBinding
import com.score.callmetest.db.DatabaseFactory
import com.score.callmetest.entity.CustomEvents
import com.score.callmetest.entity.RechargeSource
import com.score.callmetest.im.RongCloudManager
import com.score.callmetest.manager.CheckInManager
import com.score.callmetest.manager.AdjustManager
import com.score.callmetest.manager.CountdownManager
import com.score.callmetest.manager.DualChannelEventManager
import com.score.callmetest.manager.FacebookManager
import com.score.callmetest.manager.FirebaseManager
import com.score.callmetest.manager.GoodsManager
import com.score.callmetest.manager.InstallReferrerManager
import com.score.callmetest.manager.PaymentMethodManager
import com.score.callmetest.manager.RechargeManager
import com.score.callmetest.manager.StrategyManager
import com.score.callmetest.manager.UserInfoManager
import com.score.callmetest.network.GoodsInfo
import com.score.callmetest.network.LastSpecialOfferResponse
import com.score.callmetest.ui.base.BaseActivity
import com.score.callmetest.ui.home.HomeFragment
import com.score.callmetest.ui.message.MessageFragment
import com.score.callmetest.ui.message.MessageIncomingManager
import com.score.callmetest.ui.mine.MineFragment
import com.score.callmetest.ui.mycoin.MyCoinFragment
import com.score.callmetest.ui.widget.CoinRechargeDialog
import com.score.callmetest.ui.widget.PromotionDialogFragment
import com.score.callmetest.util.CustomUtils
import com.score.callmetest.util.DisplayUtils
import com.score.callmetest.util.DrawableUtils
import com.score.callmetest.util.EventBus
import com.score.callmetest.util.ThreadUtils
import com.score.callmetest.util.click
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.launch
import timber.log.Timber


class MainActivity : BaseActivity<ActivityMainBinding, MainViewModel>() {

    private val tabItems = if (StrategyManager.isReviewPkg()) listOf(
        TabItem(R.drawable.fire2, HomeFragment::class.java),
        TabItem(R.drawable.msg, MessageFragment::class.java),
//        TabItem(R.drawable.wallet, MyCoinFragment::class.java),
        TabItem(R.drawable.mine, MineFragment::class.java)
    ) else {
        listOf(
            TabItem(R.drawable.fire2, HomeFragment::class.java),
            TabItem(R.drawable.msg, MessageFragment::class.java),
            TabItem(R.drawable.wallet, MyCoinFragment::class.java),
            TabItem(R.drawable.mine, MineFragment::class.java)
        )
    }

    private val tabSvgaMap = if (StrategyManager.isReviewPkg()) mapOf(
        0 to "fire.svga",
        1 to "msg.svga",
        2 to "mine.svga",
    ) else {
        mapOf(
            0 to "fire.svga", 1 to "msg.svga", 2 to "wallet.svga", 3 to "mine.svga"
        )
    }

    private val fragments: MutableList<Fragment?>
        get() = fieldBackingFragments.also {
            if (it.size != tabItems.size) {
                if (it.size < tabItems.size) {
                    repeat(tabItems.size - it.size) { it2 -> it.add(null) }
                } else {
                    while (it.size > tabItems.size) it.removeAt(it.size - 1)
                }
            }
        }
    private var fieldBackingFragments = MutableList<Fragment?>(tabItems.size) { null }
    private var currentTabIndex = -1

    // fab1相关属性
    private var countDownListenerId: String? = null
    private var allowScrollControl = false
    private var isDialogShowing = false

    // 当前活动商品信息（用于清理倒计时监听者）
    private var currentGoodsCode: String? = null
    private var currentGoodsType: String? = null

    private val promotionViewModel: PromotionViewModel by viewModels()


    override fun getViewBinding(): ActivityMainBinding {
        return ActivityMainBinding.inflate(layoutInflater)
    }

    override fun getViewModelClass() = MainViewModel::class.java

    override fun initView() {
        setupPromotionCountdownObserver()

        ThreadUtils.runOnIO {
            // todo-dsc 融云 --connect,后续connect的位置还细考量
            // 融云获取token
            viewModel.getRongToken(onSuccess = { token ->
                // 融云R
                RongCloudManager.connect(token ?: "")
                Timber.d("rcToken---$token")
            }, onError = { e ->
                Timber.e("getRcToken---$e")
            })
        }

        setupCustomTabs()
        setupFloatingButton()
        setupFab1()

        // 检查是否有指定的Tab索引
        val targetTabIndex = intent.getIntExtra("target_tab_index", 0)
        switchTab(targetTabIndex)
    }

    override fun initData() {
        viewModel.uploadRiskInfo()
        // 初始化时加载消息数量
        loadInitialMessageCount()
        // 初始化fab1数据
        initFab1Data()
    }

    override fun initListener() {
        // 监听三方支付回调
        DualChannelEventManager.observeRechargeOrderStatus(this) {
            if (it.orderNo == null) return@observeRechargeOrderStatus
            when (it.status) {
                2 -> {
                    // 充值成功--这里只做更新操作
                    // 更新个人信息--主要刷新金币
                    UserInfoManager.refreshMyUserInfo()
                    // 更新新人促销宝箱
                    viewModel.fetchPresentedCoins()
                    lifecycleScope.launch {
                        // 更新商品列表
                        GoodsManager.refreshAllGoods()
                        // 更新签到界面
                        CheckInManager.updateCheckInDialog()
                        // 记录充值时间
                        CheckInManager.saveLastRechargeTime(System.currentTimeMillis())
                    }

                    // 充值成功，关闭弹窗
                    val fragment = supportFragmentManager.findFragmentByTag("PromotionDialog")
                    if (fragment is PromotionDialogFragment) {
                        fragment.dismiss()
                    }
                    // 向Adjust报告支付成功事件，用于广告效果追踪
                    AdjustManager.reportPaySuccessEvent(
                        orderId = it.orderNo,
                        revenue = it.payAmount,
                        currency = RechargeManager.DEFAULT_CURRENCY
                    )

                    // 向Facebook报告购买事件，用于广告优化
                    FacebookManager.logPurchaseEvent(it.payAmount,RechargeManager.DEFAULT_CURRENCY)

                    // firebase记录购买成功事件
                    FirebaseManager.logPurchaseEvent(it.payAmount,RechargeManager.DEFAULT_CURRENCY)
                }

                3 -> {
                    // 充值失败
                }
            }
        }
        // 监听可用余额
        DualChannelEventManager.observeAvailableCoins(this) { availableCoinsMessage ->
            // 更新余额
            val availableCoins = availableCoinsMessage.coins ?: 0
            UserInfoManager.myUserInfo?.availableCoins = availableCoins
        }
        // 拦截系统返回（含手势返回与预测性返回）
        onBackPressedDispatcher.addCallback(this, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                // 消费返回事件，不执行退出
                backToDesktop()
            }
        })
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        ThreadUtils.runOnIO {
            InstallReferrerManager.tryReport(this)
        }
        viewModel.init()
    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        setIntent(intent)

        // 处理新的Intent中的Tab切换请求
        intent?.let {
            val targetTabIndex = it.getIntExtra("target_tab_index", -1)
            if (targetTabIndex >= 0) {
                switchToTab(targetTabIndex)
            }
        }
    }

    private fun setupCustomTabs() {
        val tabLayout = binding.customBottomTab
        // 允许子视图超出边界显示（用于消息数量徽章）
        tabLayout.clipChildren = false
        tabLayout.clipToPadding = false
        tabLayout.removeAllViews()
        tabItems.forEachIndexed { index, tabItem ->
            val tabView = layoutInflater.inflate(R.layout.nav_tab_custom, tabLayout, false)
            val iconView = tabView.findViewById<ImageView>(R.id.tab_icon)
            val svgaView = tabView.findViewById<SVGAImageView>(R.id.tab_svga)
            val sumView = tabView.findViewById<TextView>(R.id.tab_sum)

            // 设置初始图标（未选中状态）
            iconView.setImageResource(tabItem.iconRes)
            iconView.visibility = View.VISIBLE
            svgaView.visibility = View.GONE

            // 只在消息tab（index==1）时初始化sum，具体显示由数据驱动
            if (index == 1) {
                sumView.visibility = View.GONE  // 初始隐藏，等待数据更新
                sumView.text = "0"
            } else {
                sumView.visibility = View.GONE
            }
            tabView.click {
                switchTab(index)
                // 可以显示消息弹窗
                MessageIncomingManager.setMsgListFragmentVisible(index == 1)

                if (index == 2) {
                    setFabVisible(false)
                    setFab1Visible(false)
                } else {
                    setFabVisible(true)
                    setFab1Visible(true)
                    // 通知消息页面和我的页面刷新
                    EventBus.post(CustomEvents.BottomTabSelected(index))
                }


            }
            tabLayout.addView(
                tabView, LinearLayout.LayoutParams(
                    0, LinearLayout.LayoutParams.MATCH_PARENT
                ).apply {
                    weight = 1.0f
                })
        }

        // 适配底部安全区域，防止custom_bottom_tab被系统导航栏遮挡
//        ViewCompat.setOnApplyWindowInsetsListener(tabLayout) { v, insets ->
//            val bottom = insets.getInsets(WindowInsetsCompat.Type.systemBars()).bottom
//            v.setPadding(v.paddingLeft, v.paddingTop, v.paddingRight, bottom)
//            insets
//        }
    }

    private fun switchTab(index: Int) {
        val tag = tabItems[index].clazz.simpleName
        val fragmentManager = supportFragmentManager
        val transaction = fragmentManager.beginTransaction()

        // 隐藏当前Fragment
        if (currentTabIndex != -1 && (fragments[currentTabIndex] != null)) {
            transaction.hide(fragments[currentTabIndex]!!)
        }

        // 显示或创建目标Fragment
        var fragment = fragments[index]
        if (fragment == null) {
            fragment = tabItems[index].clazz.newInstance()
            fragments[index] = fragment
            transaction.add(R.id.fragment_container, fragment, tag)
        } else {
            transaction.show(fragment)
        }

        transaction.commitAllowingStateLoss()
        currentTabIndex = index
        updateTabSelection(index)
    }

    private fun updateTabSelection(selectedIndex: Int) {
        val tabLayout = binding.root.findViewById<LinearLayout>(R.id.custom_bottom_tab)
        for (i in 0 until tabLayout.childCount) {
            val tabView = tabLayout.getChildAt(i)
            val iconView = tabView.findViewById<ImageView>(R.id.tab_icon)
            val svgaView = tabView.findViewById<SVGAImageView>(R.id.tab_svga)

            if (i == selectedIndex) {
                iconView.visibility = View.INVISIBLE
                svgaView.visibility = View.VISIBLE
                CustomUtils.playSvgaOnce(svgaView, tabSvgaMap[i])

            } else {
                iconView.visibility = View.VISIBLE
                svgaView.visibility = View.INVISIBLE
                svgaView.stopAnimation(true)
                // 清除颜色过滤器，恢复原始图标颜色
                iconView.clearColorFilter()
            }
        }
    }

    /**
     * 加载初始消息数量
     * 在应用启动时调用，确保消息数量能够正确显示
     */
    private fun loadInitialMessageCount() {
        lifecycleScope.launch {
            try {
                // 直接从数据库获取消息列表并计算未读数量
                DatabaseFactory.getDatabase(CallmeApplication.context)
                    .getCurrentUserAllMessageLists().catch { e ->
                        Timber.e("loadInitialMessageCount error: $e")
                    }.collect { messageList ->
                        // 计算总未读数量
                        val totalUnreadCount = messageList.sumOf { it.unreadCount }
                        // 更新UI（需要在主线程执行）
                        updateMessageCount(totalUnreadCount)
                        Timber.d("Initial message count loaded: $totalUnreadCount")
                    }
            } catch (e: Exception) {
                Timber.e("Failed to load initial message count: $e")
            }
        }
    }

    /**
     * 更新消息tab的数量显示
     * @param count 消息数量，0表示隐藏，大于99显示99+
     */
    fun updateMessageCount(count: Int) {
        val tabLayout = binding.root.findViewById<LinearLayout>(R.id.custom_bottom_tab)
        if (tabLayout.childCount > 1) {
            val messageTabView = tabLayout.getChildAt(1) // 消息tab是index=1
            val sumView = messageTabView.findViewById<TextView>(R.id.tab_sum)

            // 确保sumView不为null
            sumView?.let { sum ->
                if (count > 0) {
                    sum.visibility = View.VISIBLE
                    sum.text = if (count > 99) "99+" else count.toString()
                } else {
                    sum.visibility = View.GONE
                }
            }
        }
    }

    fun getCurrentTabIndex(): Int {
        return currentTabIndex
    }

    /**
     * 设置悬浮按钮
     */
    private fun setupFloatingButton() {
        if (StrategyManager.isReviewPkg()) {
            binding.fab2Layout.visibility = View.GONE
            return
        }

        // 设置圆角黑色半透明背景
        DrawableUtils.setRoundRectBackground(
            binding.tvFab2,
            ContextCompat.getColor(this, R.color.black_50),
            DisplayUtils.dp2pxInternal(9f).toFloat()
        )

        // 播放SVGA动画
        //CustomUtils.playSvga(binding.fab2, "orange_gift.svga")

        // 弹窗半屏支付界面
        binding.fab2.click {
            CoinRechargeDialog(RechargeSource.MATCH_FLOAT).show(
                supportFragmentManager, "coin_recharge"
            )
        }
    }

    /**
     * 控制悬浮按钮显示/隐藏
     * @param visible true显示，false隐藏
     */
    fun setFabVisible(visible: Boolean) {
        if (StrategyManager.isReviewPkg()) {
            return
        }
        binding.fab2Layout.visibility = if (visible) View.VISIBLE else View.GONE
    }

    /**
     * 控制fab1显示/隐藏（由HomeFragment调用）
     * @param visible true显示，false隐藏
     */
    fun setFab1Visible(visible: Boolean) {
        if (StrategyManager.isReviewPkg()) {
            return
        }

        if (visible) {
            // 只有当允许滑动控制且有数据时才显示
            if (allowScrollControl && canShowDialog()) {
                binding.fab1Layout.visibility = View.VISIBLE
                // 显示时开始播放动画
                CustomUtils.playSvga(binding.fab1, "purple_gift.svga")
            }
        } else {
            binding.fab1Layout.visibility = View.GONE
            // 隐藏时停止动画
            binding.fab1.stopAnimation(true)
        }
    }

    data class TabItem(val iconRes: Int, val clazz: Class<out Fragment>)

    /**
     * 公共方法：切换到指定的Tab页面
     * @param index Tab索引 (0: Home, 1: Message, 2: MyCoin, 3: Mine)
     */
    fun switchToTab(index: Int) {
        if (index in 0 until tabItems.size) {
            switchTab(index)
        }
    }

//    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
//        if (keyCode == KeyEvent.KEYCODE_BACK) {
//            val fm = supportFragmentManager
//            // 如果有Fragment在back stack，先让FragmentManager处理
//            if (fm.backStackEntryCount > 0) {
//                fm.popBackStack()
//            } else {
//                backToDesktop()
//            }
//            backToDesktop()
//            return true
//        }
//        return super.onKeyDown(keyCode, event)
//    }

    fun backToDesktop() {
        // 直接返回桌面，不退出应用
        val homeIntent = Intent(Intent.ACTION_MAIN)
        homeIntent.addCategory(Intent.CATEGORY_HOME)
        homeIntent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
        startActivity(homeIntent)
    }

    /**
     * 设置fab1悬浮按钮
     */
    private fun setupFab1() {
        if (StrategyManager.isReviewPkg()) {
            binding.fab1Layout.visibility = View.GONE
            return
        }

        // 设置圆角黑色半透明背景
        DrawableUtils.setRoundRectBackground(
            binding.tvFab1,
            ContextCompat.getColor(this, R.color.black_50),
            DisplayUtils.dp2pxInternal(9f).toFloat()
        )

        // 设置fab1点击事件
        binding.fab1.click {
            try {
                if (canShowDialog()) {
                    showPromotionDialogByPriority()
                } else {
                    Timber.tag("MainActivity")
                        .d("当前无法显示弹窗: loading=${viewModel.presentedCoinsLoading.value}, promotionLoading=${viewModel.promotionLoading.value}, isDialogShowing=$isDialogShowing")
                }
            } catch (e: Exception) {
                Timber.tag("MainActivity").e(e, "fab1点击处理失败")
            }
        }
    }

    /**
     * 初始化fab1数据
     */
    private fun initFab1Data() {
        if (StrategyManager.isReviewPkg()) {
            return
        }

        // 合并观察逻辑
        val promotionObserver = Observer<Any?> { _ ->
            when {

                // 1. 优先检查新人促销
                viewModel.presentedCoinsLiveData.value != null -> {
                    val data = viewModel.presentedCoinsLiveData.value!!
                    updateCountdown(
                        code = data.code,
                        type = data.type,
                        remainMilliseconds = data.surplusMillisecond ?: data.remainMilliseconds
                    )
                }
                // 2. 其次检查活动促销
                viewModel.promotionModelLiveData.value != null -> {
                    val data = viewModel.promotionModelLiveData.value!!
                    updateCountdown(
                        code = data.code,
                        type = data.type,
                        remainMilliseconds = data.remainMilliseconds
                    )
                }
                // 3. 都没有数据
                else -> {
                    // 清理倒计时监听者（使用统一的清理方式）
                    countDownListenerId?.let { listenerId ->
                        CountdownManager.removeCountdown(currentGoodsType, currentGoodsCode)
                    }
                    countDownListenerId = null
                    currentGoodsCode = null
                    currentGoodsType = null
                    binding.tvFab1.text = "00:00:00"
                    allowScrollControl = false // 禁止滑动控制显示fab1
                    // 隐藏fab1，使用统一的隐藏方法来停止动画
                    setFab1Visible(false)
                }
            }
        }

        // 同时观察两个LiveData，但使用同一个处理逻辑
        viewModel.presentedCoinsLiveData.observe(this, promotionObserver)
        viewModel.promotionModelLiveData.observe(this, promotionObserver)

        // 初始化时加载数据（优先加载新人促销）
        viewModel.fetchPresentedCoins()
    }


    /**
     * 检查是否可以显示弹窗
     */
    private fun canShowDialog(): Boolean {
        // 检查加载状态和弹窗状态
        return !(viewModel.presentedCoinsLoading.value == true || viewModel.promotionLoading.value == true || isDialogShowing)
    }

    /**
     * 更新倒计时（使用统一的商品倒计时管理）
     */
    private fun updateCountdown(code: String?, type: String?, remainMilliseconds: Long?) {
        val remainMs = remainMilliseconds ?: 0L
        if (remainMs > 0 && !code.isNullOrEmpty()) {

            // 先记录当前商品信息，避免在startCountdown时出现状态不一致
            currentGoodsCode = code
            currentGoodsType = type
            allowScrollControl = true
            // 有活动时显示fab1
            setFab1Visible(true)

            // 通过 ViewModel 启动倒计时
            promotionViewModel.startCountdown(type ?: "", code ?: "", remainMs)
        } else {
            // 清理当前商品信息
            currentGoodsCode = null
            currentGoodsType = null
            allowScrollControl = false
            // 没有有效活动时隐藏fab1
            setFab1Visible(false)
        }
    }

    /**
     * 按优先级显示促销弹窗：新人促销 > 活动促销
     */
    private fun showPromotionDialogByPriority() {
        when {
            viewModel.presentedCoinsLiveData.value != null -> {
                showPresentedCoinsDialog(viewModel.presentedCoinsLiveData.value!!)
            }

            viewModel.promotionModelLiveData.value != null -> {
                showPromotionDialog(viewModel.promotionModelLiveData.value!!)
            }

            else -> {
                // 如果都没有数据，重新获取（优先获取新人促销）
                Timber.tag("MainActivity").d("No promotion data, fetching presented coins")
            }
        }
    }

    /**
     * 显示活动促销弹窗
     */
    private fun showPromotionDialog(data: LastSpecialOfferResponse) {
        if (isDialogShowing) {
            Timber.tag("MainActivity").d("弹窗已显示，跳过显示活动促销弹窗")
            return
        }

        try {
            isDialogShowing = true

            // 获取活动信息，使用统一的商品倒计时标识
            currentGoodsCode = data.code
            currentGoodsType = data.type

            // 将 LastSpecialOfferResponse 转换为 GoodsInfo 以便进行货币化
            val goodsInfo = GoodsInfo(
                code = data.code,
                icon = data.icon,
                type = data.type,
                discount = data.discount,
                originalPrice = data.originalPrice,
                price = data.price,
                exchangeCoin = data.exchangeCoin,
                originalExchangeCoin = data.originalExchangeCoin,
                originalPriceRupee = data.originalPriceRupee,
                priceRupee = data.priceRupee,
                localPaymentPriceRupee = data.localPaymentPriceRupee,
                isPromotion = data.isPromotion,
                extraCoinPercent = data.extraCoinPercent,
                extraCoin = data.extraCoin,
                remainMilliseconds = data.remainMilliseconds,
                rechargeNum = data.rechargeNum,
                capableRechargeNum = data.capableRechargeNum,
                invitationId = data.invitationId,
                localPayOriginalPrice = data.localPayOriginalPrice,
                localPayPrice = data.localPayPrice,
                activityName = data.activityName,
                activityPic = data.activityPic,
                activitySmallPic = data.activitySmallPic
            )

            // 获取本地货币化价格
            val (priceSymbol, price) = GoodsManager.getLocaleGoodsPrice(goodsInfo)
            val (originalPriceSymbol, originalPrice) = GoodsManager.getLocaleGoodsPrice(
                goodsInfo, isOldPrice = true
            )

            val dialog = PromotionDialogFragment.newInstance(
                amount = data.exchangeCoin?.toString() ?: "0",
                addAmount = if ((data.extraCoin ?: 0) > 0) "+${data.extraCoin}" else "",
                description = data.activityName ?: "",
                price = "$priceSymbol$price",
                originPrice = "$originalPriceSymbol$originalPrice",
                layoutResId = R.layout.dialog_promotion1,
                buttonSvgaName = "btn_promotion.svga",
                remainingCount = (data.capableRechargeNum ?: 0) - (data.rechargeNum ?: 0),
                treasureBoxImageUrl = data.activityPic ?: data.activitySmallPic,
                goodsInfoType = data.type ?: "",
                goodsInfoCode = data.code ?: "",
                onButtonClickListener = {
                    try {
                        // 使用 RechargeManager 开始充值流程
                        RechargeManager.startRecharge(
                            activity = this,
                            goodsCode = data.code ?: "",
                            goodsName = data.activityName,
                            payChannel = "GP",
                            invitationId = data.invitationId,
                            isSubscription = false,
                            entry = RechargeSource.POP_PROMOTION
                        )
                    } catch (e: Exception) {
                        Timber.tag("MainActivity").e(e, "启动充值流程失败")
                    }
                })
            dialog.show(supportFragmentManager, "PromotionDialog")

            // 监听弹窗关闭事件
            supportFragmentManager.setFragmentResultListener(
                "PromotionDialog_dismissed", this
            ) { _, _ ->
                isDialogShowing = false
                supportFragmentManager.clearFragmentResultListener("PromotionDialog_dismissed")
                Timber.tag("MainActivity").d("活动促销弹窗已关闭")
            }

            Timber.tag("MainActivity").d("显示活动促销弹窗成功")
        } catch (e: Exception) {
            isDialogShowing = false
            Timber.tag("MainActivity").e(e, "显示活动促销弹窗失败")
        }
    }

    /**
     * 显示新人促销弹窗
     */
    private fun showPresentedCoinsDialog(data: GoodsInfo) {
        if (isDialogShowing) {
            Timber.tag("MainActivity").d("弹窗已显示，跳过显示新人促销弹窗")
            return
        }

        try {
            isDialogShowing = true

            // 获取活动信息，使用统一的商品倒计时标识
            currentGoodsCode = data.code
            currentGoodsType = data.type

            // 获取本地货币化价格
            val (priceSymbol, price) = GoodsManager.getLocaleGoodsPrice(data)
            val (originalPriceSymbol, originalPrice) = GoodsManager.getLocaleGoodsPrice(
                data, isOldPrice = true
            )

            val dialog = PromotionDialogFragment.newInstance(
                amount = data.exchangeCoin?.toString() ?: "0",
                description = data.activityName ?: "",
                price = "$priceSymbol$price",
                originPrice = "$originalPriceSymbol$originalPrice",
                layoutResId = R.layout.dialog_promotion,
                buttonSvgaName = "btn_promotion.svga",
                buttonEffectSvgaName = "discount_tags.svga",
                goodsInfoType = data.type ?: "",
                goodsInfoCode = data.code ?: "",
                onButtonClickListener = {
                    try {
                        // 使用 RechargeManager 开始充值流程
                        RechargeManager.startRecharge(
                            activity = this,
                            goodsCode = data.code ?: "",
                            goodsName = data.activityName,
                            payChannel = "GP",
                            invitationId = data.invitationId,
                            isSubscription = false,
                            entry = RechargeSource.POP_NEWBEE
                        )
                    } catch (e: Exception) {
                        Timber.tag("MainActivity").e(e, "启动充值流程失败")
                    }
                })
            dialog.show(supportFragmentManager, "PromotionDialog")

            // 监听弹窗关闭事件
            supportFragmentManager.setFragmentResultListener(
                "PromotionDialog_dismissed", this
            ) { _, _ ->
                isDialogShowing = false
                supportFragmentManager.clearFragmentResultListener("PromotionDialog_dismissed")
                Timber.tag("MainActivity").d("新人促销弹窗已关闭")
            }

            Timber.tag("MainActivity").d("显示新人促销弹窗成功")
        } catch (e: Exception) {
            isDialogShowing = false
            Timber.tag("MainActivity").e(e, "显示新人促销弹窗失败")
        }
    }

    private fun setupPromotionCountdownObserver() {
        promotionViewModel.countdownText.observe(this) {
            binding.tvFab1.text = it
        }

        promotionViewModel.isCountdownFinished.observe(this) {
            if (it) {
                // 隐藏按钮
                setFab1Visible(false)
            }
        }
    }

}