package com.score.callmetest.ui.visitor

import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.Canvas
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.target.Target
import com.score.callmetest.CallStatus
import com.score.callmetest.CallmeApplication.Companion.context
import com.score.callmetest.Constant
import com.score.callmetest.R
import com.score.callmetest.databinding.ItemListBottomBinding
import com.score.callmetest.databinding.ItemVisitorBinding
import com.score.callmetest.entity.RechargeSource
import com.score.callmetest.manager.AppPermissionManager
import com.score.callmetest.manager.GlobalManager
import com.score.callmetest.manager.SocketManager
import com.score.callmetest.manager.StrategyManager
import com.score.callmetest.manager.UserInfoManager
import com.score.callmetest.network.BroadcasterModel
import com.score.callmetest.network.VisitorRecord
import com.score.callmetest.ui.broadcaster.BroadcasterDetailActivity
import com.score.callmetest.ui.mine.follow.BottomState
import com.score.callmetest.ui.videocall.VideoCallActivity
import com.score.callmetest.ui.widget.InsufficientBalanceDialog
import com.score.callmetest.util.BlurUtils
import com.score.callmetest.util.TimeUtils
import com.score.callmetest.util.ToastUtils
import com.score.callmetest.util.click
import timber.log.Timber

class VisitorAdapter : RecyclerView.Adapter<RecyclerView.ViewHolder>() {
    companion object {
        private const val VIEW_TYPE_FOLLOWING = 0
        private const val VIEW_TYPE_BOTTOM = 1
    }

    private val visitors = mutableListOf<VisitorRecord>()
    private var bottomState: BottomState = BottomState.HIDDEN

    // 是否启用模糊效果
    private var mIsBlurEnabled = false

    // 模糊半径
    private var mBlurRadius = 15.0f

    // 聊天跳转回调
    private var onChatClickListener: ((VisitorRecord) -> Unit)? = null

    fun setOnChatClickListener(listener: (VisitorRecord) -> Unit) {
        this.onChatClickListener = listener
    }

    /**
     * 设置是否启用模糊效果
     * @param enabled 是否启用
     * @param radius 模糊半径，默认15.0f
     */
    fun setBlurEnabled(enabled: Boolean, radius: Float = 15.0f) {
        if (mIsBlurEnabled != enabled || mBlurRadius != radius) {
            mIsBlurEnabled = enabled
            mBlurRadius = radius
            notifyDataSetChanged()
            Timber.tag("VisitorAdapter").d("设置模糊效果: enabled=$enabled, radius=$radius")
        }
    }

    fun setBottomState(state: BottomState) {
        if (this.bottomState != state) {
            this.bottomState = state
            notifyDataSetChanged()
        }
    }

    fun setData(newVisitors: List<VisitorRecord>) {
        visitors.clear()
        visitors.addAll(newVisitors)
        notifyDataSetChanged()
    }

    /**
     * 更新来访者的在线状态
     * @param statusMap 状态映射表，key为userId，value为状态
     */
    fun updateVisitorStatus(statusMap: Map<String, String>) {
        var hasChanges = false
        for (i in visitors.indices) {
            val visitor = visitors[i]
            val userId = visitor.userId?.toString()
            if (userId != null && statusMap.containsKey(userId)) {
                val newStatus = statusMap[userId]
                if (visitor.onlineStatus != newStatus) {
                    // 创建新的VisitorRecord对象，更新在线状态
                    visitors[i] = visitor.copy(onlineStatus = newStatus)
                    hasChanges = true
                }
            }
        }

        if (hasChanges) {
            notifyDataSetChanged()
            Timber.tag("VisitorAdapter")
                .d("更新来访者在线状态完成，共更新${statusMap.size}个用户状态")
        }
    }

    /**
     * 刷新可见项，主要用于更新头像等用户信息
     * 在onResume时调用，确保显示最新的用户信息
     */
    fun refreshVisibleItems() {
        notifyDataSetChanged()
        Timber.tag("VisitorAdapter").d("刷新可见项完成，共${visitors.size}个来访者")
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            VIEW_TYPE_FOLLOWING -> {
                val binding = ItemVisitorBinding.inflate(
                    LayoutInflater.from(parent.context), parent, false
                )
                ViewHolder(binding)
            }

            VIEW_TYPE_BOTTOM -> {
                val binding = ItemListBottomBinding.inflate(
                    LayoutInflater.from(parent.context), parent, false
                )
                BottomViewHolder(binding)
            }
            // todo 异常类型主动报错，后面可以删除
            else -> throw IllegalArgumentException("Unknown view type: $viewType")
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        if (holder is ViewHolder && position < visitors.size) {
            val visitorRecord = visitors[position]
            holder.bind(visitorRecord, position)
        } else if (holder is BottomViewHolder) {
            holder.bind("Bottom")
        }
    }


    private fun startVideoCall(activity: AppCompatActivity, visitorRecord: VisitorRecord) {
        Timber.tag("VisitorAdapter").d("Starting video call with: ${visitorRecord.nickname}")

        // 1. 检查价格是否有效
        val unitPrice = visitorRecord.unitPrice ?: 0
        if (unitPrice < 0) {
            ToastUtils.showShortToast("Invalid price")
            return
        }

        if (!StrategyManager.isReviewPkg()) {
            // 2. 先检查金币是否足够
            val availableCoins = UserInfoManager.myUserInfo?.availableCoins ?: 0
            if (availableCoins < unitPrice) {
                // 弹出金币充值弹窗
                val dialog = InsufficientBalanceDialog.newInstance(
                    unitPrice,
                    RechargeSource.FOLLOW_CALL.value()
                )
                dialog.show(activity.supportFragmentManager, "insufficient_balance")
                return
            }
        }

        // 3. 检查网络连接
        if (!SocketManager.instance.isConnected()) {
            ToastUtils.showToast(context.getString(R.string.long_connection_network_offline))
            return
        }

        // 4. 获取在线状态并发起通话
        val userId = visitorRecord.userId?.toString() ?: ""
        UserInfoManager.loadOnlineStatus(
            activity.lifecycleScope, userId
        ) { status, error ->
            activity.runOnUiThread {
                if (error == null && status != null) {
                    if (CallStatus.ONLINE != status && CallStatus.AVAILABLE != status) {
                        // 状态不可用时显示toast
                        val statusText = CallStatus.getDisplayText(status)
                        val message =
                            context.getString(R.string.user_status_not_available, statusText)
                        ToastUtils.showToast(message)
                        return@runOnUiThread
                    }

                    // 5. 在线状态检查通过后，请求权限
                    AppPermissionManager.checkAndRequestCameraMicrophonePermission(
                        activity,
                        onGranted = {
                            VideoCallActivity.startOutgoing(
                                context = activity,
                                userId = userId,
                                avatarUrl = visitorRecord.avatarUrl ?: visitorRecord.avatarThumbUrl
                                ?: "",
                                nickname = visitorRecord.nickname ?: "",
                                age = visitorRecord.age?.toString() ?: "",
                                country = visitorRecord.country ?: "",
                                unitPrice = unitPrice.toString()
                            )
                        },
                        onDenied = { deniedPermissions, permanentlyDeniedPermissions ->
                        }
                    )
                } else {
                    ToastUtils.showToast(context.getString(R.string.failed_to_get_user_status))
                }
            }
        }
    }

    /**
     * 跳转到聊天
     */
    private fun startChat(visitorRecord: VisitorRecord) {
        Timber.tag("VisitorAdapter").d("Starting chat with: ${visitorRecord.nickname}")
        // 通过回调通知Activity处理聊天跳转
        onChatClickListener?.invoke(visitorRecord)
    }

    override fun getItemCount(): Int =
        visitors.size + if (bottomState != BottomState.HIDDEN) 1 else 0

    override fun getItemViewType(position: Int): Int {
        return if (bottomState != BottomState.HIDDEN && position == visitors.size) VIEW_TYPE_BOTTOM else VIEW_TYPE_FOLLOWING
    }

    private fun visitorRecordToBroadcasterModel(visitorRecord: VisitorRecord): BroadcasterModel {
        var avatarUrl = visitorRecord.avatarUrl
        visitorRecord.userId?.let { id ->
            UserInfoManager.getCachedUserInfo(id).let {
                avatarUrl = it?.avatarUrl
            }
        }
        return BroadcasterModel(
            userId = visitorRecord.userId?.toString() ?: "",
            nickname = visitorRecord.nickname,
            avatar = avatarUrl,
            gender = visitorRecord.gender,
            age = visitorRecord.age,
            country = visitorRecord.country,
            status = visitorRecord.onlineStatus ?: CallStatus.OFFLINE,
            callCoins = visitorRecord.unitPrice,
            unit = "min", // 通话单位，默认"min"
            isFriend = visitorRecord.isSpecialFollow ?: false,
            about = visitorRecord.about,
            grade = visitorRecord.level,
            analysisLanguage = visitorRecord.language,
            isSignBroadcaster = null, // VisitorRecord 中没有这个字段
            showRoomVersion = null, // VisitorRecord 中没有这个字段
            broadcasterType = visitorRecord.userType,
            avatarThumbUrl = visitorRecord.avatarThumbUrl,
            isVip = visitorRecord.isVip ?: false,
        )
    }

    inner class ViewHolder(private val binding: ItemVisitorBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(visitorRecord: VisitorRecord, position: Int) {
            // 昵称 - 根据模糊状态决定如何设置
            if (mIsBlurEnabled) {
                // 模糊模式：先设置文字内容，然后立即应用模糊
                binding.tvUsername.text = visitorRecord.nickname ?: ""
                // 延迟一帧确保TextView已经绘制完成
                binding.tvUsername.post {
                    applyBlurToTextView(binding.tvUsername)
                }
            } else {
                // 正常模式：直接设置文字内容
                binding.tvUsername.text = visitorRecord.nickname ?: ""
                // 清除可能的模糊背景
                binding.tvUsername.background = null
            }

            // 国家/地区
//            binding.tvRegion.text = visitorRecord.country ?: ""
//            binding.ivFlag.setImageResource(CountryUtils.getIconByEnName(visitorRecord.country))
            try {
                binding.lastTime.text =
                    TimeUtils.formatRelativeTime(visitorRecord.lastVisitorTime?.toLong() ?: 0L)
            } catch (e: Exception) {
                binding.lastTime.text = "unknown"
            }

            // 头像
            var avatarUrl = visitorRecord.avatarUrl ?: ""
            visitorRecord.userId?.let { id ->
                UserInfoManager.getCachedUserInfo(id).let { userInfo ->
                    if (userInfo != null) {
                        avatarUrl = userInfo.avatarUrl.toString()
                    }
                }
            }
            if (avatarUrl.isNotEmpty()) {
                Glide.with(binding.ivAvatar.context)
                    .asBitmap()
                    .load(avatarUrl)
                    .placeholder(R.drawable.placeholder)
                    .error(R.drawable.placeholder)
                    .listener(object : RequestListener<Bitmap> {
                        override fun onLoadFailed(
                            e: GlideException?,
                            model: Any?,
                            target: Target<Bitmap>,
                            isFirstResource: Boolean
                        ): Boolean {
                            // 加载失败时，如果是模糊模式，对占位符也应用模糊
                            if (mIsBlurEnabled) {
                                Handler(Looper.getMainLooper()).post {
                                    applyBlurToImageView(binding.ivAvatar)
                                }
                            }
                            return false
                        }

                        override fun onResourceReady(
                            resource: Bitmap,
                            model: Any,
                            target: Target<Bitmap>,
                            dataSource: DataSource,
                            isFirstResource: Boolean
                        ): Boolean {
                            // 图片加载成功后，如果是模糊模式，应用模糊效果
                            if (mIsBlurEnabled) {
                                Handler(Looper.getMainLooper()).post {
                                    val blurredBitmap = BlurUtils.blurBitmap(
                                        binding.ivAvatar.context, 
                                        resource, 
                                        mBlurRadius
                                    )
                                    if (blurredBitmap != null) {
                                        binding.ivAvatar.setImageBitmap(blurredBitmap)
                                    } else {
                                        binding.ivAvatar.setImageBitmap(resource)
                                    }
                                }
                                return true // 表示我们已经处理了图片设置
                            }
                            return false // 让Glide正常设置图片
                        }
                    })
                    .into(binding.ivAvatar)
            } else {
                binding.ivAvatar.setImageResource(R.drawable.placeholder)
                // 如果是模糊模式，对占位符也应用模糊
                if (mIsBlurEnabled) {
                    Handler(Looper.getMainLooper()).post {
                        applyBlurToImageView(binding.ivAvatar)
                    }
                }
            }

            // 设置状态指示器颜色
            val statusColor =
                GlobalManager.getStatusColor(visitorRecord.onlineStatus ?: CallStatus.OFFLINE)
            GlobalManager.setViewRoundBackground(binding.statusIndicator, statusColor)

            // 根据在线状态设置视频通话图标
            if (visitorRecord.onlineStatus == CallStatus.ONLINE) {
                binding.ivVideoIndicator.setImageResource(R.drawable.call_video)
            } else {
                binding.ivVideoIndicator.setImageResource(R.drawable.btn_message)
            }

            // 应用模糊效果（头像由Glide处理，文字由bind方法处理）
            // applyBlurEffect() // 现在不需要在这里调用了

            // 视频通话图标点击事件
            binding.ivVideoIndicator.click {
                val activity = itemView.context as? AppCompatActivity
                if (activity == null) {
                    ToastUtils.showToast(context.getString(R.string.page_error_unable_to_initiate_call))
                    return@click
                }

                if (visitorRecord.onlineStatus == CallStatus.ONLINE) {
                    // 在线状态：发起视频通话
                    startVideoCall(activity, visitorRecord)
                } else {
                    // 其他状态：跳转到聊天
                    startChat(visitorRecord)
                }
            }
            binding.ivAvatar.click {
                val userId = visitorRecord.userId?.toString() ?: ""
                UserInfoManager.putCachedDrawable(
                    userId,
                    binding.ivAvatar.drawable
                )
                ToBroadcasterDetailActivity(itemView.context, position)
            }

            // item点击事件
            itemView.click {
                val userId = visitorRecord.userId?.toString() ?: ""
                UserInfoManager.putCachedDrawable(
                    userId,
                    binding.ivAvatar.drawable
                )
                ToBroadcasterDetailActivity(itemView.context, position)
            }
        }

        /**
         * 应用模糊效果 - 只对名字进行模糊（头像由Glide处理）
         */
        private fun applyBlurEffect() {
            if (mIsBlurEnabled) {
                try {
                    // 对用户名应用模糊效果
                    applyBlurToTextView(binding.tvUsername)

                    Timber.tag("VisitorAdapter").d("成功应用模糊效果到用户名")
                } catch (e: Exception) {
                    Timber.tag("VisitorAdapter").e("应用模糊效果失败: ${e.message}")
                }
            } else {
                // 清除模糊效果，恢复原始状态
                clearBlurEffect()
            }
        }

        /**
         * 对ImageView应用模糊效果
         */
        private fun applyBlurToImageView(imageView: ImageView) {
            try {
                // 获取当前图片的Bitmap
                val drawable = imageView.drawable
                if (drawable is android.graphics.drawable.BitmapDrawable) {
                    val originalBitmap = drawable.bitmap
                    val blurredBitmap =
                        BlurUtils.blurBitmap(imageView.context, originalBitmap, mBlurRadius)
                    if (blurredBitmap != null) {
                        imageView.setImageBitmap(blurredBitmap)
                    }
                }
            } catch (e: Exception) {
                Timber.tag("VisitorAdapter").e("对ImageView应用模糊效果失败: ${e.message}")
            }
        }

        /**
         * 对TextView应用模糊效果
         */
        private fun applyBlurToTextView(textView: TextView) {
            try {
                // 确保TextView已经测量完成
                if (textView.width <= 0 || textView.height <= 0) {
                    textView.measure(
                        View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED),
                        View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED)
                    )
                    textView.layout(0, 0, textView.measuredWidth, textView.measuredHeight)
                }
                
                // 创建TextView的Bitmap
                val bitmap = createBitmapFromTextView(textView)
                if (bitmap != null) {
                    val blurredBitmap = BlurUtils.blurBitmap(textView.context, bitmap, mBlurRadius)
                    if (blurredBitmap != null) {
                        // 将模糊后的Bitmap设置为背景
                        textView.background = android.graphics.drawable.BitmapDrawable(
                            textView.context.resources, blurredBitmap
                        )
                        // 隐藏文字内容
                        textView.text = ""
                        Timber.tag("VisitorAdapter").d("成功对TextView应用模糊效果")
                    }
                }
            } catch (e: Exception) {
                Timber.tag("VisitorAdapter").e("对TextView应用模糊效果失败: ${e.message}")
            }
        }

        /**
         * 从TextView创建Bitmap
         */
        private fun createBitmapFromTextView(textView: TextView): Bitmap? {
            return try {
                val width = textView.width
                val height = textView.height

                if (width <= 0 || height <= 0) {
                    // 如果TextView还没有测量，使用wrap_content的默认尺寸
                    textView.measure(
                        View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED),
                        View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED)
                    )
                    val measuredWidth = textView.measuredWidth
                    val measuredHeight = textView.measuredHeight

                    if (measuredWidth <= 0 || measuredHeight <= 0) {
                        return null
                    }

                    val bitmap =
                        Bitmap.createBitmap(measuredWidth, measuredHeight, Bitmap.Config.ARGB_8888)
                    val canvas = Canvas(bitmap)
                    textView.draw(canvas)
                    bitmap
                } else {
                    val bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
                    val canvas = Canvas(bitmap)
                    textView.draw(canvas)
                    bitmap
                }
            } catch (e: Exception) {
                Timber.tag("VisitorAdapter").e("从TextView创建Bitmap失败: ${e.message}")
                null
            }
        }

        /**
         * 清除模糊效果
         */
        private fun clearBlurEffect() {
            try {
                // 恢复TextView的原始状态
                binding.tvUsername.background = null
                // 注意：这里需要重新设置用户名，因为之前被清空了
                // 这个会在bind方法中重新设置

                // 头像的模糊效果会在下次bind时重新加载原始图片
                // 由于Glide会重新加载，这里不需要特殊处理
            } catch (e: Exception) {
                Timber.tag("VisitorAdapter").e("清除模糊效果失败: ${e.message}")
            }
        }
    }

    // 跳转到主播详情页面
    private fun ToBroadcasterDetailActivity(context: Context, position: Int) {
        if (position != RecyclerView.NO_POSITION && position < visitors.size) {
            val visitorRecord = visitors[position]
            val broadcasterModel = visitorRecordToBroadcasterModel(visitorRecord)
            val intent = Intent(context, BroadcasterDetailActivity::class.java)
            intent.putExtra(Constant.BROADCASTER_MODEL, broadcasterModel)
            context.startActivity(intent)
        }
    }

    inner class BottomViewHolder(private val binding: ItemListBottomBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bind(text: String) {
            when (bottomState) {
                BottomState.LOADING -> {
                    // 显示加载动画，隐藏静态底部
                    binding.layoutLoading.visibility = View.VISIBLE
                    binding.layoutFinished.visibility = View.GONE
                }

                BottomState.FINISHED -> {
                    // 隐藏加载动画，显示静态底部
                    binding.layoutLoading.visibility = View.GONE
                    binding.layoutFinished.visibility = View.VISIBLE
                    binding.tvBottomText.text = text
                }

                BottomState.HIDDEN -> {
                    // 隐藏所有（这种情况下不应该调用bind，但为了安全起见）
                    binding.layoutLoading.visibility = View.GONE
                    binding.layoutFinished.visibility = View.GONE
                }
            }
        }
    }

}