package com.score.callmetest.ui.widget

import android.app.Dialog
import android.content.Context
import android.graphics.Color
import android.graphics.drawable.GradientDrawable
import android.os.Bundle
import android.text.SpannableString
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.graphics.toColorInt
import com.score.callmetest.R
import com.score.callmetest.databinding.DialogVipBinding
import com.score.callmetest.entity.RechargeSource
import com.score.callmetest.manager.GoodsManager
import com.score.callmetest.manager.RechargeManager
import com.score.callmetest.network.GoodsInfo
import com.score.callmetest.ui.widget.VipItemCardView.OnCardClickListener
import com.score.callmetest.util.ActivityUtils
import com.score.callmetest.util.DisplayUtils
import com.score.callmetest.util.DrawableUtils
import com.score.callmetest.util.click

// agree 按钮在下面，cancel 在上面

class VipDialog(
    context: Context,
) : Dialog(context, androidx.appcompat.R.style.Theme_AppCompat_Dialog) {
    private var binding: DialogVipBinding = DialogVipBinding.inflate(LayoutInflater.from(context))
    private var selectedItem: VipItemCardView? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(binding.root)
        setCancelable(true)

        // 设置全屏/居中
        window?.setLayout(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.WRAP_CONTENT
        )
        // 隐藏状态栏
        window?.decorView?.systemUiVisibility = (
                View.SYSTEM_UI_FLAG_FULLSCREEN or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                )
        window?.setBackgroundDrawableResource(R.color.transparent)
        // 点击空白区域可取消
        setCanceledOnTouchOutside(true)

        // 设置内容左右37dp边距
        val paddingPx = DisplayUtils.dp2pxInternal(37f)
        binding.root.setPadding(
            paddingPx,
            DisplayUtils.dp2pxInternal(50f),
            paddingPx,
            DisplayUtils.dp2pxInternal(99f)
        )
        binding.iconView.click {
            // 拦截点击事件，不关闭弹窗
        }
        binding.dialogContent.click {
            // 拦截点击事件，不关闭弹窗
        }
        binding.root.click {
            dismiss()
        }

        setupVipCards()

        // 高亮显示指定文本
        val spannableString = SpannableString("Become VIP to unlock 7 privileges")
        val highlightText = "7"
        val highlightColor = "#FFFF4444".toColorInt()
        val startIndex = spannableString.indexOf(highlightText)
        if (startIndex != -1) {
            spannableString.setSpan(
                android.text.style.ForegroundColorSpan(highlightColor),
                startIndex,
                startIndex + highlightText.length,
                android.text.Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
            )
        }
        binding.vipDescInfo.text = spannableString

        binding.bottomShadow.background = DrawableUtils.createGradientDrawable(
            colors = intArrayOf("#00FFFFFF".toColorInt(), Color.WHITE, Color.WHITE),
            orientation = GradientDrawable.Orientation.TOP_BOTTOM
        )


        binding.vipButton.click {
            if (selectedItem != null) {
                ActivityUtils.getTopActivity()?.let {
                    RechargeManager.startRecharge(
                        activity = it,
                        goodsCode = selectedItem!!.getData()!!.code.toString(),
                        entry = RechargeSource.SUBSCRIBE_DIALOG,
                    )
                    dismiss()
                }
            }
        }
        refreshData()
    }

    private fun setupVipCards() {
        val list = GoodsManager.getCachedVipGoods()
        selectedItem = binding.vipCard1month
        if (list.size > 0) {
            binding.vipCard1month.setData(
                GoodsManager.getCachedVipGoods()[0],
                R.drawable.vip_item_icon_1,
                style = VipItemCardView.Style.DARK
            ).setOnCardClickListener(object : OnCardClickListener {
                override fun onCardClick(data: GoodsInfo?) {
                    selectedItem = binding.vipCard1month
                    refreshData()
                }
            })
        } else {
            binding.vipCard1month.visibility = View.GONE
            binding.vipCard3month.visibility = View.GONE
            binding.vipCard12month.visibility = View.GONE
        }

        if (list.size > 1) {
            binding.vipCard3month.setData(
                GoodsManager.getCachedVipGoods()[1],
                R.drawable.vip_item_icon_2
            ).setOnCardClickListener(object : OnCardClickListener {
                override fun onCardClick(data: GoodsInfo?) {
                    selectedItem = binding.vipCard3month
                    refreshData()
                }
            })
        } else {
            binding.vipCard3month.visibility = View.GONE
            binding.vipCard12month.visibility = View.GONE
        }

        if (list.size > 2) {
            binding.vipCard12month.setData(
                GoodsManager.getCachedVipGoods()[2],
                R.drawable.vip_item_icon_3
            ).setOnCardClickListener(object : OnCardClickListener {
                override fun onCardClick(data: GoodsInfo?) {
                    selectedItem = binding.vipCard12month
                    refreshData()
                }
            })
        } else {
            binding.vipCard12month.visibility = View.GONE
        }
    }

    private fun refreshData() {
        binding.vipCard1month.updateUI(VipItemCardView.Style.LIGHT)
        binding.vipCard3month.updateUI(VipItemCardView.Style.LIGHT)
        binding.vipCard12month.updateUI(VipItemCardView.Style.LIGHT)
        selectedItem?.updateUI(VipItemCardView.Style.DARK)
        // 设置VIP特权描述项数据
        setupVipPrivileges()
    }

    /**
     * 设置VIP特权描述项数据
     */
    private fun setupVipPrivileges() {
        // 1. 金币奖励
        binding.vipDescCoinsBonus.setDataWithHighlight(
            R.drawable.vip_item_1,
            "Coins bonus",
            "Get ${selectedItem?.getData()?.exchangeCoin} Coins after activating",
            "${selectedItem?.getData()?.exchangeCoin}"
        )

        // 2. 充值奖励
        binding.vipDescRechargeBonus.setDataWithHighlight(
            R.drawable.vip_item_2,
            "Recharge bonus",
            "Up to 20% extra coins for each recharge",
            "20%"
        )

        // 3. 通话折扣
        binding.vipDescCallDiscount.setDataWithHighlight(
            R.drawable.vip_item_3,
            "Call Discount",
            "10% off video calls",
            "10%"
        )

        // 4. 匹配通话优惠
        binding.vipDescMatchCall.setDataWithHighlight(
            R.drawable.vip_item_4,
            "Match Call offer",
            "10% off Match Call",
            "10%"
        )

        // 5. 免费文字聊天
        binding.vipDescTextChat.setData(
            R.drawable.vip_item_5,
            "Free Text Chat",
            "Text Chat all for free"
        )

        // 6. 查看照片相册
        binding.vipDescPhotoAlbum.setData(
            R.drawable.vip_item_6,
            "View her photo album",
            "Check all her photos for free"
        )

        // 7. 查看访客列表
        binding.vipDescVisitorList.setData(
            R.drawable.vip_item_7,
            "View Visitor list",
            "Unlock a list of girls interested in you"
        )

        // 8. 精致VIP状态
        binding.vipDescStatus.setData(
            R.drawable.vip_item_8,
            "Exquisite VIP status",
            "Distinguished status is unique"
        )

    }


}