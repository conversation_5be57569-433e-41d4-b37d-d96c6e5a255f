package com.score.callmetest.manager

import android.content.Context
import com.score.callmetest.network.GetEquityReq
import com.score.callmetest.network.GetEquityResp
import com.score.callmetest.network.NetworkResult
import com.score.callmetest.network.RetrofitUtils
import com.score.callmetest.ui.widget.VipDialog
import com.score.callmetest.ui.widget.VipSuccessDialog
import com.score.callmetest.util.ActivityUtils
import com.score.callmetest.util.EventBus
import com.score.callmetest.util.ThreadUtils

object VipManager {
    private var vipEquity: GetEquityResp? = null
    private var lastGetTimeStamp = 0L

    // 记录状态
    private var isVipCache: Boolean? = null
    private var vipDate: String? = null

    fun getEquity(
        callback: (GetEquityResp?) -> Unit = {}
    ) {
        if (vipEquity != null) {
            callback.invoke(vipEquity)
        }
        // 30s钟内不重复获取
        if (vipEquity == null || System.currentTimeMillis() - lastGetTimeStamp > 60 * 1000L) {
            lastGetTimeStamp = System.currentTimeMillis()
            ThreadUtils.runOnIO {
                for (vipGoods in GoodsManager.getCachedVipGoods()) {
                    val resp =
                        RetrofitUtils.dataRepository.getUserEquity(GetEquityReq(vipGoods.goodsId))
                    val equity = if (resp is NetworkResult.Success) {
                        resp.data
                    } else null

                    if (equity != null) {
                        if (vipEquity == null) {
                            EventBus.post(VipChangeEvent(equity))
                        }
                        vipEquity = equity

                        // 初始化标记
                        if (isVipCache == null) {
                            isVipCache = isVip()
                        }
                        if (isVip()) {
                            if (vipDate == null) {
                                vipDate = getExpiredTime()
                            }
                        }
                        break
                    }
                }
                ThreadUtils.runOnMain {
                    // 刚成为vip,
                    if (isVipCache == false && isVip()) {
                        isVipCache = true
                        vipDate = getExpiredTime()
                        showVipSuccessDialog()
                    } else {
                        // vip续期
                        if (vipDate != getExpiredTime()) {
                            isVipCache = true
                            vipDate = getExpiredTime()
                            showVipSuccessDialog()
                        }
                    }
                    callback.invoke(vipEquity)
                }
            }
        }
    }

    fun showVipDialog() {
        if (GoodsManager.getCachedVipGoods().isNotEmpty()) {
            ActivityUtils.getTopActivity()?.let {
                VipDialog(it).show()
            }
        }
    }

    fun showVipSuccessDialog() {
        if (isVip() && getExpiredTime() != null) {
            ActivityUtils.getTopActivity()?.let {
                VipSuccessDialog(it).show()
            }
        }
    }

    fun isVip(): Boolean {
//        return false
        return vipEquity?.myEquity?.hasEquity ?: false
    }

    fun getExpiredTime(): String? {
        return vipEquity?.myEquity?.expireTime
    }
}

data class VipChangeEvent(val data: GetEquityResp)