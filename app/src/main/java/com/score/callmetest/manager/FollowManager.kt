package com.score.callmetest.manager

import android.content.Context
import com.score.callmetest.CallmeApplication
import com.score.callmetest.R
import com.score.callmetest.entity.ChatMessageEntity
import com.score.callmetest.entity.MessageEvents
import com.score.callmetest.entity.MessageStatus
import com.score.callmetest.entity.MessageType
import com.score.callmetest.network.AddFriendRequest
import com.score.callmetest.network.FollowModel
import com.score.callmetest.network.NetworkResult
import com.score.callmetest.network.RetrofitUtils
import com.score.callmetest.network.UnFriendRequest
import com.score.callmetest.network.UserFollowPageRequest
import com.score.callmetest.network.UserInfo
import com.score.callmetest.ui.widget.FollowEvent
import com.score.callmetest.util.EventBus
import com.score.callmetest.util.SharePreferenceUtil
import com.score.callmetest.util.logAsTag
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import timber.log.Timber
import java.util.UUID
import kotlin.random.Random

/**
 * 关注管理类
 * 负责管理用户的关注、粉丝、互关等相关功能
 */
object FollowManager {

    private val coroutineScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    // SharedPreferences key前缀，用于存储首次关注标记
    private const val FIRST_FOLLOW_KEY_PREFIX = "first_follow_"

    /**
     * 刷新事件
     * type = 1 关注，取消关注，通知刷新
     * type = 2 粉丝列表刷新，通知mineFragment更新数据
     */
    class FollowListRefreshEvent (val type: Int)

    /**
     * 关注类型枚举
     */
    object FollowType {
        const val FRIENDS = 1      // 互关列表
        const val FOLLOWERS = 2    // 粉丝列表
        const val FOLLOWING = 3    // 关注列表
    }

    /**
     * 分页请求结果回调
     */
    interface FollowPageCallback {
        fun onSuccess(list: List<FollowModel>)
        fun onError(errorMsg: String)
        fun onLoading(isLoading: Boolean)
    }

    /**
     * 关注/取关操作回调
     */
    interface FollowActionCallback {
        fun onSuccess()
        fun onError(errorMsg: String)
    }

    /**
     * 加载关注列表（type=3）
     * @param limit 每页数量，默认15
     * @param page 页码，默认1
     * @param callback 回调接口
     */
    fun loadFollowingList(
        limit: Int = 15,
        page: Int = 1,
        callback: FollowPageCallback
    ) {
        fetchUserFollowPage(
            type = FollowType.FOLLOWING,
            limit = limit,
            page = page,
            callback = callback
        )
    }

    /**
     * 加载粉丝列表（type=2）
     * @param limit 每页数量，默认15
     * @param page 页码，默认1
     * @param callback 回调接口
     */
    fun loadFollowersList(
        limit: Int = 15,
        page: Int = 1,
        callback: FollowPageCallback
    ) {
        fetchUserFollowPage(
            type = FollowType.FOLLOWERS,
            limit = limit,
            page = page,
            callback = callback
        )
    }

    /**
     * 加载互关列表（type=1）
     * @param limit 每页数量，默认15
     * @param page 页码，默认1
     * @param callback 回调接口
     */
    fun loadFriendsList(
        limit: Int = 15,
        page: Int = 1,
        callback: FollowPageCallback
    ) {
        fetchUserFollowPage(
            type = FollowType.FRIENDS,
            limit = limit,
            page = page,
            callback = callback
        )
    }

    /**
     * 关注用户（添加好友）
     * @param scope 协程作用域
     * @param userId 用户ID
     * @param callback 操作结果回调
     * @param sendEvent 是否发送EventBus事件，默认为true
     */
    fun followUser(scope: CoroutineScope? = null,userId: String, callback: FollowActionCallback, sendEvent: Boolean = true) {
        val newScope = scope ?: coroutineScope
        newScope.launch {
            try {
                val request = AddFriendRequest(userId)
                val response = RetrofitUtils.dataRepository.addFriend(request)
                if (response is NetworkResult.Success && response.data == true) {
                    if (sendEvent) {
                        EventBus.post(FollowEvent(userId, true))
                    }
                    callback.onSuccess()
                    
                    // 关注成功后，刷新用户信息来更新数量
                    refreshFollowNotify()

                    UserInfoManager.getUserInfo(userId,scope = coroutineScope) { userInfo ->
                        // 处理首次关注逻辑（包括发送模拟消息）
                        handleFollowSuccess(userInfo)
                    }
                } else {
                    if (sendEvent) {
                        EventBus.post(FollowEvent(userId, false))
                    }
                    callback.onError( "关注失败--$response")
                }
            } catch (e: Exception) {
                callback.onError(e.message ?: "网络异常")
            }
        }
    }

    /**
     * 取关用户（删除好友）
     * @param userId 用户ID
     * @param callback 操作结果回调
     */
    fun unfollowUser(scope: CoroutineScope? = null,userId: String, callback: FollowActionCallback) {
        val newScope = scope ?: coroutineScope
        newScope.launch {
            try {
                val request = UnFriendRequest(userId)
                val response = RetrofitUtils.dataRepository.unfriend(request)
                if (response is NetworkResult.Success && response.data == true) {
                    EventBus.post(FollowEvent(userId, false))
                    callback.onSuccess()
                    // 取关成功后，刷新用户信息来更新数量
                    refreshFollowNotify()
                } else {
                    callback.onError( "取关失败--$response")
                }
            } catch (e: Exception) {
                callback.onError(e.message ?: "网络异常")
            }
        }
    }

    /**
     * 通用分页请求
     * @param type 关系类型 1-friends;2-followers;3-following
     * @param limit 每页数量，默认15
     * @param page 页码，默认1
     * @param callback 回调接口
     */
    private fun fetchUserFollowPage(
        type: Int,
        limit: Int = 15,
        page: Int = 1,
        callback: FollowPageCallback
    ) {
        callback.onLoading(true)
        coroutineScope.launch {
            try {
                val request = UserFollowPageRequest(type = type, limit = limit, page = page)
                val response = RetrofitUtils.dataRepository.getUserFollowPage(request)
                if (response is NetworkResult.Success) {
                    val list = response.data ?: emptyList()
                    // 记录日志
                    Timber.tag("FollowManager").d("type : ${type} : ${list.toString()}")
                    callback.onSuccess(list)
                } else {
                    callback.onError( "请求失败--$response")
                }
            } catch (e: Exception) {
                callback.onError(e.message ?: "网络异常")
            } finally {
                callback.onLoading(false)
            }
        }
    }

    /**
     * 简化版关注操作，返回Boolean结果
     * @param userId 用户ID
     * @return 是否成功
     */
    suspend fun followUserSync(userId: String): Boolean {
        return try {
            val request = AddFriendRequest(userId)
            val response = RetrofitUtils.dataRepository.addFriend(request)
            response is NetworkResult.Success && response.data == true
        } catch (e: Exception) {
            false
        }
    }

    /**
     * 刷新用户信息并通知关注数量变化
     */
    private fun refreshFollowNotify(type: Int = 1) {
            // 发送事件通知
            EventBus.post(FollowListRefreshEvent(type))
    }

    /**
     * 是否关注
     */
    fun isFollow(userInfo: UserInfo?): Boolean{
        return userInfo?.isFriend == true
    }

    /**
     * 检查是否是首次关注该用户
     * @param userId 用户ID
     * @return true表示是首次关注，false表示已经关注过
     */
    private fun isFirstTimeFollow(userId: String): Boolean {
        val key = FIRST_FOLLOW_KEY_PREFIX + userId
        return !SharePreferenceUtil.getBoolean(key, false)
    }

    /**
     * 标记已关注过该用户
     * @param userId 用户ID
     */
    private fun markAsFollowed(userId: String) {
        val key = FIRST_FOLLOW_KEY_PREFIX + userId
        SharePreferenceUtil.putBoolean(key, true)
    }

    /**
     * 从XML资源中随机选择一条关注消息
     * @return 随机选择的消息内容
     */
    private fun getRandomFollowMessage(): String {
        val context = CallmeApplication.context
        val messages = context.resources.getStringArray(R.array.auto_follow_messages)
        return if (messages.isNotEmpty()) {
            messages[Random.nextInt(messages.size)]
        } else {
            "Thank you for following me!" // 默认消息
        }
    }

    /**
     * 发送模拟的关注消息
     * @param fromUser 发送消息的用户（主播）
     * @param toUserId 接收消息的用户ID（当前用户）
     */
    private fun sendSimulatedFollowMessage(fromUser: UserInfo, toUserId: String) {
        coroutineScope.launch {
            try {
                // 延迟2-5秒发送，模拟真实场景
                val delayTime = Random.nextLong(2000, 5000)
                delay(delayTime)

                val randomMessage = getRandomFollowMessage()
                
                // 创建模拟消息
                val simulatedMessage = ChatMessageEntity(
                    messageId = UUID.randomUUID().toString(),
                    currentUserId = toUserId,
                    senderId = fromUser.userId!!,
                    senderName = fromUser.nickname ?: "Unknown",
                    senderAvatar = fromUser.avatarUrl ?: "",
                    receiverId = toUserId,
                    content = randomMessage,
                    messageType = MessageType.TEXT,
                    status = MessageStatus.RECEIVED,
                    timestamp = System.currentTimeMillis(),
                    isCurrentUser = false
                )

                // 通过EventBus发送消息事件
                EventBus.post(MessageEvents.AutoFollowMessage(simulatedMessage))
                
                Timber.tag("FollowManager").d("发送模拟关注消息: ${fromUser.userId} -> $toUserId, 内容: $randomMessage")
                
            } catch (e: Exception) {
                Timber.tag("FollowManager").w(e, "发送模拟关注消息失败")
            }
        }
    }

    /**
     * 处理关注成功后的逻辑，包括首次关注消息发送
     * @param followedUser 被关注的用户信息
     */
    private fun handleFollowSuccess(followedUser: UserInfo?) {
        if (followedUser == null || followedUser.userId.isNullOrEmpty()) return
        
        val currentUserId = UserInfoManager.myUserInfo?.userId ?: return
        
        // 检查是否是首次关注
        if (isFirstTimeFollow(followedUser.userId)) {
            // 标记为已关注
            markAsFollowed(followedUser.userId)
            
            // 发送模拟消息（模拟主播发送给当前用户）
            sendSimulatedFollowMessage(followedUser, currentUserId)
        }
    }

}
