package com.score.callmetest.manager

import androidx.appcompat.app.AppCompatActivity
import androidx.room.concurrent.AtomicBoolean
import com.score.callmetest.CallmeApplication
import com.score.callmetest.R
import com.score.callmetest.entity.CustomEvents
import com.score.callmetest.network.CheckinRewardResp
import com.score.callmetest.network.CheckinStatusResp
import com.score.callmetest.network.NetworkResult
import com.score.callmetest.network.RetrofitUtils
import java.text.SimpleDateFormat
import java.util.*
import com.score.callmetest.ui.widget.checkin.CheckInDialog
import com.score.callmetest.ui.widget.checkin.CheckInSuccessDialog
import com.score.callmetest.util.EventBus
import com.score.callmetest.util.ThreadUtils
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import timber.log.Timber
import kotlin.collections.forEach
import android.os.Handler
import android.os.Looper
import com.score.callmetest.UserCategory
import com.score.callmetest.network.CheckinRewardsReq
import com.score.callmetest.util.ActivityUtils
import com.score.callmetest.util.SharePreferenceUtil
import com.score.callmetest.util.ToastUtils

/**
 * 签到管理器
 * 统一管理签到功能
 */
object CheckInManager {

    private val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())

    // 缓存签到奖励列表
    private var cachedCheckInRewards: List<CheckinRewardResp>? = null

    // 缓存签到状态信息
    private var cachedCheckInStatus: CheckinStatusResp? = null

    // 缓存数据的日期（格式：yyyy-MM-dd）
    private var cachedStatusDate: String? = null
    private var cachedRewardsDate: String? = null

    private var isCheckInDialogShowing = AtomicBoolean(false)

    suspend fun init(){
        // 获取签到奖励信息及签到状态
        fetchCheckInRewards()
        fetchCheckInStatus()
        startAutoShowCheckInDialog()
    }

    /**
     * 获取当前日期字符串（格式：yyyy-MM-dd）
     */
    private fun getCurrentDateString(): String {
        return dateFormat.format(Date())
    }

    /**
     * 检查缓存数据是否过期（是否为当天数据）
     * @return true表示数据有效，false表示数据过期需要重新获取
     */
    private fun isCacheDataValid(): Boolean {
        val currentDate = getCurrentDateString()
        val isValid = cachedStatusDate == currentDate &&
                cachedRewardsDate == currentDate &&
                cachedCheckInRewards != null &&
                cachedCheckInStatus != null

        if (!isValid) {
            Timber.d("缓存数据检查: 当前日期=$currentDate, 缓存日期=$cachedStatusDate, 数据有效=$isValid")
        }

        return isValid
    }

    /**
     * 显示签到弹窗
     */
    fun showCheckInDialog(activity: AppCompatActivity,scope: CoroutineScope? = null) {
        try {
            // 检查缓存数据是否有效（包括日期检查）
            if (isCacheDataValid()) {
                Timber.d("显示签到弹窗 - 使用有效的缓存数据")
                showCheckInDialog(activity)
            } else {
                Timber.d("缓存数据无效或过期，重新获取数据")
                // 启动协程获取签到奖励数据
                val newScope = scope ?: CoroutineScope(Dispatchers.IO)
                newScope.launch {
                    refreshCache(false)
                    // 获取完成后在主线程显示弹窗
                    ThreadUtils.runOnMain {
                        if (isCacheDataValid()) {
                            Timber.d("重新获取数据后显示签到弹窗")
                            showCheckInDialog(activity)
                        } else {
                            Timber.e("获取签到奖励数据失败，无法显示弹窗")
                        }
                    }
                }
            }
        } catch (e: Exception) {
            Timber.e(e, "显示签到弹窗失败")
        }
    }
    
    private fun showCheckInDialog(activity: AppCompatActivity) {
        if(isCheckInDialogShowing.get()) {
            Timber.d("签到弹窗已经弹窗--请勿重复弹窗")
            return
        }
        val dialog = CheckInDialog()
        dialog.show(activity.supportFragmentManager, "check_in_dialog")
        isCheckInDialogShowing.set(true)
        Timber.d("签到弹窗已展示，停止自动展示循环")
        // 停止自动展示循环
        stopAutoShowLoop()

    }
    
    fun dismissCheckInDialog() {
        isCheckInDialogShowing.set(false)
    }

    //<editor-fold desc="最后一次充值记录及判断">
    // 保存最新充值时间
    private const val LAST_RECHARGE_TIME = "last_recharge_time"
    // 免费用户注册48小时
    private const val FREE_USER_CHECK_IN_DELAY_HOURS = 48
    // 付费用户每120小时
    private const val PAID_USER_CHECK_IN_DELAY_HOURS = 120

    /**
     * 保存最后一次充值时间
     * @param [time] 时间
     */
    fun saveLastRechargeTime(time: Long) {
        SharePreferenceUtil.putLong(LAST_RECHARGE_TIME, time)
    }

    /**
     * 距离最后一次充值时间过了多少个小时
     */
    private fun getHoursSinceLastRecharge(): Int {
        val lastRechargeTime = SharePreferenceUtil.getLong(LAST_RECHARGE_TIME, 0)
        return if (lastRechargeTime == 0L) {
            -1
        } else {
            val currentTime = System.currentTimeMillis()
            val hours = (currentTime - lastRechargeTime) / 1000 / 60 / 60
            hours.toInt()
        }
    }

    /**
     * 距离注册时间过了多久
     */
    fun getHoursSinceRegister(): Int {
        val registerTime = UserInfoManager.myUserInfo?.createTime ?: 0
        return if (registerTime == 0L) {
            Int.MAX_VALUE
        } else {
            val currentTime = System.currentTimeMillis()
            val hours = (currentTime - registerTime) / 1000 / 60 / 60
            hours.toInt()
        }
    }

    /**
     * 免费用户注册[FREE_USER_CHECK_IN_DELAY_HOURS]小时内(不含[FREE_USER_CHECK_IN_DELAY_HOURS])是否充值过
     *
     * @return true表示[FREE_USER_CHECK_IN_DELAY_HOURS]小时内充值过，false表示[FREE_USER_CHECK_IN_DELAY_HOURS]小时内未充值
     */
    fun hasRechargedInFreeUserCheckInDelay(): Boolean {
        val hours = getHoursSinceLastRecharge()
        if(hours == -1){
            // 没有充值过
            // 检查注册时间距离限制是否超过[FREE_USER_CHECK_IN_DELAY_HOURS]小时
            return getHoursSinceRegister() < FREE_USER_CHECK_IN_DELAY_HOURS
        }
        // 充值过--->使用付费用户逻辑
        // 正常来说不会出现这种情况
        return hasRechargedInPaidUserCheckInDelay()
    }

    /**
     * 付费用户每[PAID_USER_CHECK_IN_DELAY_HOURS]小时(不含[PAID_USER_CHECK_IN_DELAY_HOURS])内是否充值过
     *
     * @return true表示每[PAID_USER_CHECK_IN_DELAY_HOURS]小时内充值过，false表示每[PAID_USER_CHECK_IN_DELAY_HOURS]小时内未充值
     */
    fun hasRechargedInPaidUserCheckInDelay(): Boolean {
        val hours = getHoursSinceLastRecharge()
        if(hours == -1){
            // 当前设备没有充值过---就当他[PAID_USER_CHECK_IN_DELAY_HOURS]内没充值
            return false
        }
        return getHoursSinceLastRecharge() < PAID_USER_CHECK_IN_DELAY_HOURS
    }
    //</editor-fold>


    //<editor-fold desc="自动弹出签到弹窗相关">

    // sp 记录当天auto弹窗次数
    private const val AUTO_SHOW_ATTEMPT_COUNT = "auto_show_attempt_count"
    private const val AUTO_SHOW_ATTEMPT_TIME = "auto_show_attempt_time"
    private val autoShowHandler = Handler(Looper.getMainLooper())

    /**
     * 每天最多自动弹出3次
     *
     * 9月1日修改---改为不限制次数(Int.MAX_VALUE-21亿多次应该够用了)
     */
    private const val MAX_AUTO_SHOW_ATTEMPTS_PER_DAY = Int.MAX_VALUE

    private const val AUTO_SHOW_RETRY_DELAY_MS = 3000L // 重试延迟3秒

    private var autoShowRunnable: Runnable = Runnable {
        try {
            // 检查是否满足启动条件
            if (!canAutoShowDialog()) {
                Timber.d("dsc--不需要自动展示签到弹窗，停止自动展示循环")
                return@Runnable
            }
            // 数据是否过期
            if(!isCacheDataValid()){
                Timber.d("dsc--缓存数据无效或过期，重新获取数据")
                ThreadUtils.runOnIO {
                    refreshCache(false)
                }
                autoShowHandler.postDelayed(autoShowRunnable, AUTO_SHOW_RETRY_DELAY_MS)
                return@Runnable
            }
            // 签到弹窗是否正在显示
            if (isCheckInDialogShowing.get()) {
                Timber.d("dsc--签到弹窗正在显示，等下一次展示")
                autoShowHandler.postDelayed(autoShowRunnable, AUTO_SHOW_RETRY_DELAY_MS)
                return@Runnable
            }
            // 如果在后台，则等切换到前台再展示
            if(AppLifecycleManager.isAppInBackground()){
                autoShowHandler.postDelayed(autoShowRunnable, AUTO_SHOW_RETRY_DELAY_MS)
                return@Runnable
            }

            // 显示弹窗
            ActivityUtils.getTopActivity()?.let { acty ->
                showCheckInDialog(acty as AppCompatActivity)
                addAttemptCount()
            }

        } catch (e: Exception) {
            stopAutoShowLoop()
            Timber.d("dsc--循环发生错误，停止自动展示循环")
        }
    }

    /**
     * 自动启动签到弹窗
     * 满足条件时展示弹窗，不满足时延迟3秒重试
     * 一天最多弹出3次，弹窗展示过或用户签到完成后停止循环
     *
     */
    fun startAutoShowCheckInDialog() {
        try {
            // 检查是否满足启动条件
            if (!canAutoShowDialog()) {
                Timber.d("dsc--不满足自动启动签到弹窗条件")
                return
            }

            // 停止之前可能运行的自动展示循环
            stopAutoShowLoop()

            // 开始自动展示循环
            autoShowHandler.postDelayed(autoShowRunnable, AUTO_SHOW_RETRY_DELAY_MS)

        } catch (e: Exception) {
            Timber.e(e, "dsc--自动启动签到弹窗失败")
        }
    }

    /**
     * 检查是否可以自动展示签到弹窗
     */
    private fun canAutoShowDialog(): Boolean {
        // 检查今天是否已经签到完成
        if (isCheckedInToday()) {
            Timber.d("dsc--今天已签到，无需自动展示弹窗")
            return false
        }

        if(hasReachedMaxAttempts()){
            return  false
        }

        return true
    }

    /**
     * 隔天重置自动弹出次数
     */
    private fun resetAttemptCount() {
        SharePreferenceUtil.putInt(AUTO_SHOW_ATTEMPT_COUNT, 0)
    }

    /**
     * 当天自动弹出次数+1
     */
    private fun addAttemptCount() {
        val count = SharePreferenceUtil.getInt(AUTO_SHOW_ATTEMPT_COUNT, 0)
        SharePreferenceUtil.putInt(AUTO_SHOW_ATTEMPT_COUNT, count + 1)
        SharePreferenceUtil.putLong(AUTO_SHOW_ATTEMPT_TIME, System.currentTimeMillis())
    }

    /**
     * 检查是否已达到当天最大尝试次数
     */
    private fun hasReachedMaxAttempts(): Boolean {
        val time = SharePreferenceUtil.getLong(AUTO_SHOW_ATTEMPT_TIME, 0L)
        val currentDate = getCurrentDateString()
        val count = if(dateFormat.format(Date(time)) != currentDate){
            // 不是同一天
            resetAttemptCount()
            0
        }else {
            SharePreferenceUtil.getInt(AUTO_SHOW_ATTEMPT_COUNT, 0)
        }

        if (count >= MAX_AUTO_SHOW_ATTEMPTS_PER_DAY) {
            Timber.d("dsc--今天已达到最大自动弹出次数($MAX_AUTO_SHOW_ATTEMPTS_PER_DAY)，停止自动展示")
            return true
        }

        return false
    }

    /**
     * 停止自动展示循环
     */
    fun stopAutoShowLoop() {
        autoShowHandler.removeCallbacks(autoShowRunnable)
        Timber.d("dsc--停止自动展示签到弹窗循环")
    }

    //</editor-fold>

    /**
     * 显示签到成功奖励弹窗
     * @param activity Activity实例
     * @param reward 奖励数据
     */
    fun showCheckInSuccessDialog(activity: AppCompatActivity, reward: CheckinRewardResp) {
        try {
            val dialog = CheckInSuccessDialog.newInstance(reward)
            dialog.show(activity.supportFragmentManager, "check_in_success_dialog")
            Timber.d("显示签到成功弹窗: reward=$reward")
        } catch (e: Exception) {
            Timber.e(e, "显示签到成功弹窗失败")
        }
    }
    
    /**
     * 获取连续签到天数--从0开始
     */
    fun getContinuousCheckInDays(): Int {
        return cachedCheckInStatus?.consecutiveDays ?: 0
    }
    
    /**
     * 获取总签到天数
     */
    fun getTotalCheckInDays(): Int {
        return cachedCheckInStatus?.totalDays ?: 0
    }
    
    /**
     * 检查今天是否已签到
     */
    fun isCheckedInToday(): Boolean {
        return cachedCheckInStatus?.isTodayChecked ?: false
    }
    
    /**
     * 执行签到
     */
    fun checkIn(scope: CoroutineScope? = null,onSuccess: (() -> Unit)? = null, onError: ((String) -> Unit)? = null) {
        val newScope = scope ?: CoroutineScope(Dispatchers.IO)
        newScope.launch {
            try {
                // 检查缓存数据是否有效（包括日期检查）
                if(!isCacheDataValid()){
                    Timber.d("签到前检查: 缓存数据无效或过期，重新获取数据")
                    refreshCache(false)
                    // 重新检查数据是否有效
                    if(!isCacheDataValid()) {
                        ThreadUtils.runOnMain {
                            onError?.invoke(CallmeApplication.context.getString(R.string.check_in_failed))
                        }
                        return@launch
                    }
                }
                // 当前是否签到过
                if (isCheckedInToday()) {
                    ThreadUtils.runOnMain {
                        onError?.invoke(CallmeApplication.context.getString(R.string.check_in_had_checked))
                    }
                    return@launch
                }
                // 执行签到
                checkIn(onSuccess,onError)
                
            } catch (e: Exception) {
                Timber.e(e, "签到失败")
                ThreadUtils.runOnMain {
                    onError?.invoke(CallmeApplication.context.getString(R.string.check_in_failed))
                }
            }
        }
    }

    /**
     * 检查是否为连续的一天
     */
    private fun isConsecutiveDay(): Boolean {
        return getContinuousCheckInDays() != 0
    }

    /**
     * 执行签到
     */
    private suspend fun checkIn(onSuccess: (() -> Unit)? = null, onError: ((String) -> Unit)? = null) {
        try {
            val response = RetrofitUtils.dataRepository.checkin()
            if (response is NetworkResult.Success) {
                val checkinResult = response.data
                // 检查签到结果
                checkinResult?.run {
                    Timber.d("签到接口成功-->${status}-->$message")
                    when (status) {
                        "success" -> {
                            // 签到成功
                            EventBus.post(CustomEvents.CheckInAvailable(false))
                            ThreadUtils.runOnMain {
                                onSuccess?.invoke()
                            }
                        }
                        "failed" -> {
                            // 签到失败
                            ThreadUtils.runOnMain {
                                onError?.invoke(CallmeApplication.context.getString(R.string.check_in_failed))
                            }
                        }
                        "already_checked" -> {
                            // 今日已签到
                            ThreadUtils.runOnMain {
                                onError?.invoke(CallmeApplication.context.getString(R.string.check_in_had_checked))
                            }
                        }
                        "device_limited" -> {
                            // 设备限制
                            ThreadUtils.runOnMain {
                                onError?.invoke(CallmeApplication.context.getString(R.string.check_in_device_limited))
                            }
                        }
                        else -> {
                            // 其他错误
                            ThreadUtils.runOnMain {
                                onError?.invoke(CallmeApplication.context.getString(R.string.check_in_failed))
                            }
                        }
                    }
                }
                // 更新签到状态
                // 更新奖励列表
                ThreadUtils.runOnIO {
                    refreshCache(true)
                }
            } else {
                Timber.d("签到失败")
                ThreadUtils.runOnMain {
                    onError?.invoke(CallmeApplication.context.getString(R.string.check_in_failed))
                }
            }

        } catch (e: Exception) {
            Timber.e(e, "签到失败")
            ThreadUtils.runOnMain {
                onError?.invoke(CallmeApplication.context.getString(R.string.check_in_failed))
            }
        }
    }

    /**
     * 刷新缓存数据
     *
     * @param force 是否强制刷新
     */
    suspend fun refreshCache(force: Boolean = false){
        if(cachedRewardsDate != getCurrentDateString() || force) {
            fetchCheckInRewards()
        }
        if(cachedStatusDate != getCurrentDateString() || force) {
            fetchCheckInStatus()
        }
    }

    /**
     * 充值完更新签到弹窗
     */
    suspend fun updateCheckInDialog(){
        if(!isCacheDataValid()){
            // 过期
            refreshCache(true)
        }else if(!SharePreferenceUtil.contains(LAST_RECHARGE_TIME)){
            // 存在---之前就是收费用户
            // 如果之前是免费--现在是收费，那就更新
            // 如果之前也是收费，那就无需刷新数据
            refreshCache(true)
        }
        if(isCheckInDialogShowing.get()){
            // 更新界面
            EventBus.post(CustomEvents.CheckInDialogUpdate)
        }
    }

    /**
     * 获取签到奖励列表
     */
    suspend fun fetchCheckInRewards() {
        try {
            val request = CheckinRewardsReq(
                userCategory = if(VipManager.isVip()){
                    UserCategory.VIP
                }else if(UserInfoManager.myUserInfo?.isRecharge == true){
                    UserCategory.PAID_USER
                }else{
                    UserCategory.FREE_USER
                }
            )
            val response = RetrofitUtils.dataRepository.checkinRewards(request)
            if (response is NetworkResult.Success) {
                cachedCheckInRewards = response.data
                // 更新缓存日期为当前日期
                cachedRewardsDate = getCurrentDateString()

                Timber.d("获取签到奖励列表成功，缓存日期更新为: $cachedStatusDate")
            } else {
                Timber.d("获取签到奖励列表失败")
            }
        } catch (e: Exception) {
            Timber.e(e, "获取签到奖励列表失败")
        }
    }

    /**
     * 获取签到状态
     */
    suspend fun fetchCheckInStatus() {
        try {
            val response = RetrofitUtils.dataRepository.checkinStatus()
            if (response is NetworkResult.Success) {
                cachedCheckInStatus = response.data

                // 更新缓存日期为当前日期
                cachedStatusDate = getCurrentDateString()

                // 更新图标
                EventBus.post(CustomEvents.CheckInAvailable(isAvailable = response.data?.canCheckin == true))

                Timber.d("签到状态获取成功，缓存日期更新为: $cachedStatusDate")
            } else {
                Timber.d("签到状态获取失败")
            }
        } catch (e: Exception) {
            Timber.e(e, "签到状态获取失败")
        }
    }



    
    /**
     * 获取签到奖励配置
     * 优先返回缓存的奖励配置，如果没有则返回null
     */
    fun getCheckInRewards(): List<CheckinRewardResp>? {
        return cachedCheckInRewards
    }

    fun logout() {
        cachedCheckInRewards = null
        cachedCheckInStatus = null
        cachedStatusDate = null
        
        // 清理自动启动相关状态
        stopAutoShowLoop()
        
        Timber.d("dsc--清除签到缓存数据和自动启动状态")
    }
}
