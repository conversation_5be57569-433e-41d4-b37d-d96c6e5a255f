<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root_view"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <!--排名前三-->
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/constraint_layout_root"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            tools:background="@color/black">

            <LinearLayout
                android:id="@+id/line_rich_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:orientation="horizontal"
                android:paddingVertical="11dp"
                android:paddingStart="17dp"
                android:paddingEnd="21dp"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <ImageView
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_marginStart="17dp"
                    android:src="@drawable/clock" />

                <TextView
                    android:id="@+id/tv_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="6dp"
                    android:fontFamily="@font/roboto_bold"
                    android:textColor="#FDFF00"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    tools:text="Rank of February" />

            </LinearLayout>

            <!-- 排名前三 -->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/rank_platform"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/line_rich_time">

                <ImageView
                    android:layout_width="220dp"
                    android:layout_height="254dp"
                    android:layout_marginTop="14dp"
                    android:src="@drawable/rank_light"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <!-- 头像 -->
                <com.score.callmetest.ui.widget.CircleIconButton
                    android:id="@+id/iv_avatar1"
                    android:layout_width="78dp"
                    android:layout_height="78dp"
                    android:layout_marginTop="12dp"
                    android:scaleType="centerCrop"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:src="@drawable/placeholder" />

                <!-- 头像框 -->
                <ImageView
                    android:id="@+id/line_avatar_frame1"
                    android:layout_width="102dp"
                    android:layout_height="102dp"
                    android:elevation="2dp"
                    android:src="@drawable/avatar_rank1"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/tv_avatar1_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:fontFamily="@font/roboto_bold"
                    android:maxLines="2"
                    android:textAlignment="center"
                    android:textColor="#fff"
                    android:textSize="13sp"
                    android:textStyle="bold"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/line_avatar_frame1"
                    tools:text="Kathleen12346465789" />


                <!-- 头像 -->
                <com.score.callmetest.ui.widget.CircleIconButton
                    android:id="@+id/iv_avatar2"
                    android:layout_width="60dp"
                    android:layout_height="60dp"
                    android:layout_marginStart="11dp"
                    android:layout_marginTop="-36dp"
                    android:scaleType="centerCrop"
                    app:layout_constraintStart_toStartOf="@+id/image_rank_platform"
                    app:layout_constraintTop_toTopOf="@+id/image_rank_platform"
                    tools:src="@drawable/placeholder" />

                <!-- 头像框 -->
                <ImageView
                    android:id="@+id/line_avatar_frame2"
                    android:layout_width="74dp"
                    android:layout_height="74dp"
                    android:layout_marginStart="4dp"
                    android:layout_marginTop="-43dp"
                    android:elevation="2dp"
                    android:src="@drawable/avatar_rank2"
                    app:layout_constraintStart_toStartOf="@+id/image_rank_platform"
                    app:layout_constraintTop_toTopOf="@+id/image_rank_platform" />

                <TextView
                    android:id="@+id/tv_avatar2_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:fontFamily="@font/roboto_bold"
                    android:maxLines="2"
                    android:textAlignment="center"
                    android:textColor="#fff"
                    android:textSize="13sp"
                    android:textStyle="bold"
                    app:layout_constraintEnd_toEndOf="@id/line_avatar_frame2"
                    app:layout_constraintStart_toStartOf="@+id/line_avatar_frame2"
                    app:layout_constraintTop_toBottomOf="@+id/line_avatar_frame2"
                    tools:text="Seconssaasasasd" />
                <!-- 头像 -->
                <com.score.callmetest.ui.widget.CircleIconButton
                    android:id="@+id/iv_avatar3"
                    android:layout_width="60dp"
                    android:layout_height="60dp"
                    android:layout_marginTop="-13dp"
                    android:layout_marginEnd="11dp"
                    android:scaleType="centerCrop"
                    app:layout_constraintEnd_toEndOf="@+id/image_rank_platform"
                    app:layout_constraintTop_toTopOf="@+id/image_rank_platform"
                    tools:src="@drawable/placeholder" />

                <!-- 头像框 -->
                <ImageView
                    android:id="@+id/line_avatar_frame3"
                    android:layout_width="74dp"
                    android:layout_height="74dp"
                    android:layout_marginTop="-20dp"
                    android:layout_marginEnd="4dp"
                    android:elevation="2dp"
                    android:src="@drawable/avatar_rank3"
                    app:layout_constraintEnd_toEndOf="@+id/image_rank_platform"
                    app:layout_constraintTop_toTopOf="@+id/image_rank_platform" />

                <TextView
                    android:id="@+id/tv_avatar3_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:fontFamily="@font/roboto_bold"
                    android:maxLines="1"
                    android:textAlignment="center"
                    android:textColor="#fff"
                    android:textSize="13sp"
                    android:textStyle="bold"
                    app:layout_constraintEnd_toEndOf="@+id/line_avatar_frame3"
                    app:layout_constraintStart_toStartOf="@+id/line_avatar_frame3"
                    app:layout_constraintTop_toBottomOf="@+id/line_avatar_frame3"
                    tools:text="Third1234567890" />

                <ImageView
                    android:id="@+id/image_rank_platform"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="22dp"
                    android:layout_marginTop="17dp"
                    android:src="@drawable/rank_platform"
                    app:layout_constraintTop_toBottomOf="@+id/tv_avatar1_name" />
            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </FrameLayout>

    <!--排行榜-->
    <RelativeLayout
        android:id="@+id/main_content_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@android:color/transparent"
        android:orientation="vertical"
        app:layout_behavior="com.google.android.material.bottomsheet.BottomSheetBehavior">

        <!-- 排行榜顶部  -->
        <com.opensource.svgaplayer.SVGAImageView
            android:id="@+id/rank_top"
            android:layout_width="match_parent"
            android:layout_height="70dp"
            android:clickable="false"
            android:focusable="false"
            android:scaleType="center"
            tools:background="#000" />

        <androidx.core.widget.NestedScrollView
            android:id="@+id/detail_scrollview"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_below="@id/rank_top"
            android:fillViewport="true"
            android:overScrollMode="never">
            <!-- 排行榜内容 -->
            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="#fff">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_rich"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginBottom="60dp"
                    android:background="@android:color/white"
                    android:nestedScrollingEnabled="true"
                    android:overScrollMode="never" />

                <!--为空的时候的占位页面-->
                <ViewStub
                    android:id="@+id/empty_view"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:inflatedId="@+id/layout_empty_rv_parent"
                    android:layout="@layout/layout_empty_rv_bg"
                    android:visibility="gone" />
            </FrameLayout>
        </androidx.core.widget.NestedScrollView>
    </RelativeLayout>

    <!--个人排名-->
    <LinearLayout
        android:id="@+id/line_rank_mine"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:background="@drawable/bg_rank_mine"
        android:orientation="horizontal">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="19dp"
            android:layout_marginTop="19dp"
            android:layout_marginBottom="8dp"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_mine_rank"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:fontFamily="@font/roboto_bold"
                android:textColor="#FF8C00"
                android:textSize="18sp"
                android:textStyle="bold"
                tools:text="24" />
            <!-- 头像 -->
            <com.score.callmetest.ui.widget.CircleIconButton
                android:id="@+id/tv_mine_avatar"
                android:layout_width="50dp"
                android:layout_height="50dp"
                android:layout_gravity="center"
                android:layout_marginStart="13dp"
                android:scaleType="centerCrop"
                tools:src="@drawable/placeholder" />

            <TextView
                android:id="@+id/tv_mine_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginStart="15dp"
                android:fontFamily="@font/roboto_medium"
                android:textColor="#42424D"
                android:textSize="14sp"
                android:textStyle="bold"
                tools:text="Nicole" />
        </LinearLayout>
    </LinearLayout>
</androidx.coordinatorlayout.widget.CoordinatorLayout>

