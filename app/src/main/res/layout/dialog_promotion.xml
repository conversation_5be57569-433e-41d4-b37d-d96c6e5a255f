<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:clipChildren="false"
    android:clipToPadding="false">


    <!-- 主内容容器 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/promotion"
        android:layout_width="355dp"
        android:layout_height="415dp"
        android:background="@drawable/bg_promotion"
        android:clipChildren="false"
        android:clipToPadding="false"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">
        <!-- 关闭按钮 -->
        <ImageView
            android:id="@+id/iv_close"
            android:layout_width="23dp"
            android:layout_height="23dp"
            android:layout_marginEnd="22dp"
            android:layout_marginTop="36dp"
            android:src="@drawable/promotion_dialog_cancel"
            app:layout_constraintVertical_bias="0"
            app:layout_constraintHorizontal_bias="1"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="94dp"
            android:maxWidth="220dp"
            android:textAlignment="center"
            android:fontFamily="@font/montserrat_alternates_black"
            android:text="Worthful Discount for new user"
            android:textColor="#fff"
            android:textSize="18sp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"/>
        <!-- SVGA按钮动画 -->
        <com.score.callmetest.ui.widget.AlphaSVGAImageView
            android:id="@+id/svga_discount"
            android:layout_width="90dp"
            android:layout_height="90dp"
            android:layout_gravity="center"
            android:layout_marginEnd="38dp"
            app:layout_constraintTop_toBottomOf="@+id/tv_title"
            app:layout_constraintEnd_toEndOf="parent"/>


        <LinearLayout
            android:id="@+id/line_sum"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginTop="45dp"
            android:gravity="center"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_title">
            <ImageView
                android:layout_width="46dp"
                android:layout_height="46dp"
                android:layout_marginStart="4dp"
                android:layout_gravity="center"
                android:src="@drawable/coin_promotion"/>
            <!-- 金额文本 -->
            <TextView
                android:id="@+id/tv_amount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="#FF5B00"
                android:textSize="40sp"
                android:textStyle="bold"
                android:textAlignment="center"
                android:fontFamily="@font/roboto_bold"
                tools:text="500"
                android:layout_marginStart="8dp"/>


        </LinearLayout>

        <LinearLayout
            android:id="@+id/line_countdowmn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="13dp"
            android:gravity="center"
            app:layout_constraintBottom_toTopOf="@+id/fl_button_container"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <!-- 小时 -->
            <TextView
                android:id="@+id/tv_hour"
                android:layout_width="29dp"
                android:layout_height="29dp"
                android:background="@drawable/bg_countdown"
                android:gravity="center"
                android:textSize="13sp"
                android:textColor="#FFFFFF"
                tools:text="12" />

            <!-- 分隔符 -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="4dp"
                android:text=":"
                android:textSize="16sp"
                android:textColor="@color/black"
                android:gravity="center" />

            <!-- 分钟 -->
            <TextView
                android:id="@+id/tv_min"
                android:layout_width="29dp"
                android:layout_height="29dp"
                android:background="@drawable/bg_countdown"
                android:gravity="center"
                android:textSize="13sp"
                android:textColor="#FFFFFF"
                tools:text="34" />

            <!-- 分隔符 -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="4dp"
                android:text=":"
                android:textSize="13sp"
                android:textColor="@color/black"
                android:gravity="center" />

            <!-- 秒 -->
            <TextView
                android:id="@+id/tv_seconds"
                android:layout_width="29dp"
                android:layout_height="29dp"
                android:background="@drawable/bg_countdown"
                android:gravity="center"
                android:textSize="13sp"
                android:textColor="#FFFFFF"
                tools:text="56" />

        </LinearLayout>

        <!-- 按钮容器 -->
        <FrameLayout
            android:id="@+id/fl_button_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:clipChildren="false"
            android:clipToPadding="false"
            android:layout_marginBottom="13dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent">

            <!-- SVGA按钮动画 -->
            <com.score.callmetest.ui.widget.AlphaSVGAImageView
                android:id="@+id/svga_button"
                android:layout_width="270dp"
                android:layout_height="65dp"
                android:layout_gravity="center" />

            <!-- 按钮文字 -->
            <com.score.callmetest.ui.widget.AlphaLinearLayout
                android:id="@+id/price_layout"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:gravity="center"
                android:layout_marginBottom="5dp"
                android:orientation="horizontal"
                android:paddingHorizontal="10dp">

                <TextView
                    android:id="@+id/tv_price"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    tools:text="$0.99"
                    android:textAlignment="center"
                    android:textColor="#fff"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tv_old_price"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="4dp"
                    android:fontFamily="@font/roboto_regular"
                    tools:text="$19.99"
                    android:textColor="#CCFFFFFF"
                    android:textSize="13sp" />

            </com.score.callmetest.ui.widget.AlphaLinearLayout>
        </FrameLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>


</androidx.constraintlayout.widget.ConstraintLayout>
