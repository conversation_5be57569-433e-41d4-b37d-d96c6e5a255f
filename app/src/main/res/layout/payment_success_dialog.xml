<?xml version="1.0" encoding="utf-8"?>
<FrameLayout android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_dialog_rounded"
    xmlns:android="http://schemas.android.com/apk/res/android">

        <ImageView
            android:id="@+id/dialog_bg"
            android:layout_width="match_parent"
            android:layout_height="90dp"
            android:background="@drawable/dialog_bg_green" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingHorizontal="32dp"
            android:paddingTop="33dp"
            android:paddingBottom="25dp">
            <LinearLayout
                android:id="@+id/dialog_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center">
                <com.opensource.svgaplayer.SVGAImageView
                    android:id="@+id/svga_coin"
                    android:layout_width="21dp"
                    android:layout_height="21dp" />
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Top up success"
                    android:layout_marginStart="7dp"
                    android:textColor="@color/black"
                    android:textSize="18sp"
                    android:fontFamily="@font/roboto_black" />

            </LinearLayout>
            <LinearLayout
                android:id="@+id/dialog_coin"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginTop="41dp"
                android:gravity="center">
               <ImageView
                   android:layout_width="47dp"
                   android:layout_height="47dp"
                   android:src="@drawable/coin_promotion"/>
                <TextView
                    android:id="@+id/dialog_coin_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="+500"
                    android:layout_marginStart="7dp"
                    android:textColor="#FF7500"
                    android:textSize="45sp"
                    android:fontFamily="@font/roboto_black" />

            </LinearLayout>






            <com.score.callmetest.ui.widget.AlphaLinearLayout
                android:id="@+id/btn_ok_layout"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:layout_marginTop="41dp"
                android:background="@drawable/black_rounded_button_bg"
                android:gravity="center"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/btn_confirm"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:text="OK"
                    android:textColor="#FFFFFF"
                    android:textSize="15sp"/>

            </com.score.callmetest.ui.widget.AlphaLinearLayout>

        </LinearLayout>


    </FrameLayout>


