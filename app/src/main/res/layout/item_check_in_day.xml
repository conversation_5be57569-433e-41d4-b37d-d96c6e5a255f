<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <LinearLayout
        android:id="@+id/dayLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:layout_marginEnd="4dp"
        android:background="@drawable/bg_check_in_day"
        android:gravity="center"
        android:orientation="vertical"
        android:padding="8dp"
        app:layout_constraintTop_toTopOf="parent"
        tools:alpha="1"
        tools:background="@drawable/bg_check_in_selected">

        <TextView
            android:id="@+id/tv_day"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/roboto_medium"
            android:textColor="@color/check_in_gray"
            android:textSize="12sp"
            tools:text="Day 1" />

        <ImageView
            android:id="@+id/iv_reward"
            android:layout_width="28dp"
            android:layout_height="50dp"
            android:layout_marginTop="8dp"
            android:scaleType="fitCenter"
            tools:src="@drawable/icon_check_in_small_coin" />

        <TextView
            android:id="@+id/tv_num"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:fontFamily="@font/roboto_medium"
            android:textColor="@color/black"
            android:textSize="12sp"
            tools:text="+60" />

    </LinearLayout>

    <!--  已签到  -->
    <FrameLayout
        android:id="@+id/checkedLayout"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/dayLayout"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible">

        <View
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginHorizontal="4dp"
            android:layout_marginTop="4dp"
            android:background="#4AFF5353"
            android:visibility="gone" />

        <ImageView
            android:id="@+id/iv_check"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_gravity="top|end"
            android:src="@drawable/icon_checked_in"
            android:visibility="visible"
            tools:visibility="visible" />

    </FrameLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
