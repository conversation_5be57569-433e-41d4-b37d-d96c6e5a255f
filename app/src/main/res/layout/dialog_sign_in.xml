<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_gift_bottomsheet"
    android:orientation="vertical"
    android:paddingTop="13dp"
    android:paddingBottom="15dp">

    <LinearLayout
        android:id="@+id/line_context"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:layout_width="54dp"
            android:layout_height="54dp"
            android:layout_marginStart="23dp"
            android:layout_gravity="start|center_vertical"
            android:src="@drawable/coins6" />

        <TextView
            android:id="@+id/tv_content_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginStart="10dp"
            android:layout_marginEnd="54dp"
            android:fontFamily="@font/roboto_medium"
            android:gravity="start"
            android:textColor="@color/black"
            android:textSize="14sp"
            tools:text="@string/coin_dialog_limit" />

    </LinearLayout>

    <ImageView
        android:id="@+id/divide_line"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="7dp"
        android:paddingHorizontal="20dp"
        android:src="@drawable/dividing_line"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/line_context" />


    <LinearLayout
        android:id="@+id/line_coin"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:orientation="horizontal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/divide_line">

        <ImageView
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:layout_gravity="center"
            android:layout_marginStart="18dp"
            android:src="@drawable/coin" />

        <!-- 顶部金币数量 -->
        <TextView
            android:id="@+id/tv_coin_balance"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginStart="2dp"
            android:layout_weight="1"
            android:fontFamily="@font/roboto_medium"
            android:text=" 1000"
            android:textColor="@color/black"
            android:textSize="14sp" />

        <com.score.callmetest.ui.widget.AlphaImageView
            android:id="@+id/coin_service"
            android:layout_width="25dp"
            android:layout_height="25dp"
            android:layout_gravity="center"
            android:layout_marginEnd="18dp"
            android:src="@drawable/customer_service_white"
            android:tint="@color/black" />
    </LinearLayout>

    <com.score.callmetest.ui.widget.RechargeSubView
        android:id="@+id/recharge_sub_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="7dp"
        android:visibility="invisible"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/line_coin" />
</androidx.constraintlayout.widget.ConstraintLayout>