<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/chat_bg"
    tools:context=".ui.message.MessageFragment">

    <androidx.appcompat.widget.AppCompatImageView
        android:layout_width="match_parent"
        android:layout_height="90dp"
        android:background="@drawable/main_bg"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- 清理未读按钮 -->
    <com.score.callmetest.ui.widget.AlphaImageView
        android:id="@+id/btn_clear"
        android:layout_width="26dp"
        android:layout_height="26dp"
        android:layout_marginEnd="18dp"
        android:scaleType="fitCenter"
        android:src="@drawable/clean"
        app:layout_constraintBottom_toBottomOf="@id/tab_message"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/tab_message" />

    <!-- 签到入口 -->
    <com.score.callmetest.ui.widget.AlphaSVGAImageView
        android:id="@+id/btn_check_in"
        android:layout_width="34dp"
        android:layout_height="34dp"
        android:layout_marginEnd="6dp"
        app:layout_constraintEnd_toStartOf="@id/btn_clear"
        app:layout_constraintTop_toTopOf="@id/tab_message" />

    <!--  tab  -->
    <com.google.android.material.tabs.TabLayout
        android:id="@+id/tab_message"
        style="@style/CustomTabStyle"
        android:layout_width="0dp"
        android:layout_height="44dp"
        android:layout_marginStart="4dp"
        android:layout_marginEnd="4dp"
        android:background="@android:color/transparent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toStartOf="@id/btn_check_in"
        app:layout_constraintTop_toTopOf="parent"
        app:tabGravity="fill"
        app:tabIndicatorHeight="0dp"
        app:tabRippleColor="@color/transparent"
        app:tabMode="scrollable" />

    <!-- ViewPager2用于承载内容 -->
    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/view_pager"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:overScrollMode="never"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tab_message" />

</androidx.constraintlayout.widget.ConstraintLayout>