<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/home_bg">

    <LinearLayout
        android:id="@+id/main_content_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginBottom="-8dp"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/line_tag1"
            android:layout_width="match_parent"
            android:layout_height="44dp"
            android:gravity="center_vertical"
            android:baselineAligned="false"
            android:orientation="horizontal">
            <!-- 一级Tab -->
            <com.google.android.material.tabs.TabLayout
                android:id="@+id/tab_layout1"
                style="@style/CustomTabStyle"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_marginStart="10dp"
                android:layout_marginEnd="5dp"
                android:layout_weight="1"
                android:background="@color/transparent"
                app:tabGravity="fill"
                app:tabIndicatorHeight="0dp"
                app:tabMode="scrollable"
                app:tabRippleColor="@color/transparent" />

            <!-- 签到入口 -->
            <com.score.callmetest.ui.widget.AlphaSVGAImageView
                android:id="@+id/btn_check_in"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:scaleType="fitCenter"
                android:layout_gravity="top"
                android:layout_marginEnd="7dp" />

            <FrameLayout
                android:id="@+id/rank_list"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_gravity="center"
                android:layout_marginEnd="10dp">

                <ImageView
                    android:id="@+id/image_rank_list"
                    android:layout_width="34dp"
                    android:layout_height="34dp"
                    android:layout_gravity="center"
                    android:scaleType="fitCenter"
                    android:src="@drawable/ranking" />

            </FrameLayout>
        </LinearLayout>

        <LinearLayout
            android:id="@+id/line_tag2"
            android:layout_width="match_parent"
            android:layout_height="24dp"
            android:baselineAligned="false"
            android:orientation="horizontal">
            <!-- 二级Tab -->
            <com.google.android.material.tabs.TabLayout
                android:id="@+id/tab_layout2"
                style="@style/CustomTabStyle"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:background="@color/transparent"
                app:tabGravity="fill"
                app:tabIndicatorHeight="0dp"
                app:tabMode="scrollable"
                app:tabRippleColor="@color/transparent" />

            <FrameLayout
                android:id="@+id/country_filter"
                android:layout_width="40dp"
                android:layout_height="match_parent"
                android:layout_gravity="center"
                android:layout_marginEnd="15dp">

                <ImageView
                    android:id="@+id/image_home_country"
                    android:layout_width="20dp"
                    android:layout_height="match_parent"
                    android:layout_gravity="end"
                    android:src="@drawable/map_language" />

                <TextView
                    android:id="@+id/tv_home_country"
                    android:layout_width="22dp"
                    android:layout_height="wrap_content"
                    android:layout_gravity="bottom|end"
                    android:gravity="center"
                    android:text="All"
                    android:textSize="8sp"
                    android:textStyle="bold" />
            </FrameLayout>

        </LinearLayout>

        <!-- ViewPager2用于承载内容 -->
        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/view_pager"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginHorizontal="11dp"
            android:layout_weight="1"
            android:overScrollMode="never" />

    </LinearLayout>

    <!-- 悬浮按钮1：orange_gift.svga -->
    <FrameLayout
        android:id="@+id/fab1_layout"
        android:layout_width="54dp"
        android:layout_height="58dp"
        android:layout_gravity="bottom|end"
        android:layout_marginEnd="20dp"
        android:layout_marginBottom="105dp"
        android:visibility="gone">

        <!-- 悬浮按钮 -->
        <com.opensource.svgaplayer.SVGAImageView
            android:id="@+id/fab1"
            android:layout_width="54dp"
            android:layout_height="54dp"
            android:layout_gravity="top|center_horizontal"
            android:clickable="true"
            android:focusable="true"
            android:scaleType="center" />

        <!-- 计时器，只用TextView -->
        <TextView
            android:id="@+id/fab1_timer_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom|center_horizontal"
            android:background="@android:color/transparent"
            android:paddingHorizontal="2dp"
            android:paddingVertical="2dp"
            android:text="00:00:00"
            android:textAlignment="center"
            android:textColor="@android:color/white"
            android:textSize="10sp"
            android:textStyle="bold" />
    </FrameLayout>

    <FrameLayout
        android:id="@+id/bottom_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|center_horizontal"
        android:layout_marginBottom="30dp">

        <com.score.callmetest.ui.widget.AlphaFrameLayout
            android:id="@+id/flash_chat_layout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp">

            <LinearLayout
                android:id="@+id/flash_content_layout"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginHorizontal="2dp"
                android:layout_marginTop="2dp"
                android:gravity="center"
                android:orientation="horizontal"
                android:paddingVertical="7dp"
                android:paddingStart="13dp"
                android:paddingEnd="37dp">

                <FrameLayout
                    android:id="@+id/flash_icon_layout"
                    android:layout_width="55dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="14dp"
                    android:orientation="horizontal">

                    <com.score.callmetest.ui.widget.CircleIconButton
                        android:id="@+id/flash_icon_1"
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:visibility="gone"
                        app:strokeColor="#91FFFFFF"
                        app:strokeWidth="1.5dp" />

                    <com.score.callmetest.ui.widget.CircleIconButton
                        android:id="@+id/flash_icon_2"
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        app:strokeColor="#91FFFFFF"
                        app:strokeWidth="1.5dp" />
                </FrameLayout>

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/flash_text"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:includeFontPadding="false"
                        android:text="Free Match"
                        android:textColor="@android:color/white"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:typeface="sans" />

                    <TextView
                        android:id="@+id/flash_tip"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="1dp"
                        android:includeFontPadding="false"
                        android:textColor="@android:color/white"
                        android:textSize="12sp" />
                </LinearLayout>
            </LinearLayout>


        </com.score.callmetest.ui.widget.AlphaFrameLayout>

        <com.score.callmetest.ui.widget.AlphaFrameLayout
            android:id="@+id/vip_tip_layout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:paddingHorizontal="3dp"
            android:layout_marginStart="20dp">

            <com.opensource.svgaplayer.SVGAImageView
                android:id="@+id/vip_tip_bg_svga"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:gravity="center"
                android:paddingHorizontal="7dp"
                android:paddingTop="3dp"
                android:paddingBottom="5dp"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="15dp"
                    android:layout_height="15dp"
                    android:layout_marginEnd="2dp"
                    android:src="@drawable/vip_dialog_icon" />

                <TextView
                    android:id="@+id/vip_discount_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:lineHeight="11dp"
                    android:textColor="@android:color/white"
                    android:textSize="10sp" />
            </LinearLayout>
        </com.score.callmetest.ui.widget.AlphaFrameLayout>

    </FrameLayout>
</FrameLayout>