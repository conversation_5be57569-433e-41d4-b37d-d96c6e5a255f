<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/vip_bg">

    <!--顶部导航栏-->
    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:elevation="0dp">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="44dp">

            <com.score.callmetest.ui.widget.AlphaImageView
                android:id="@+id/iv_back"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:layout_centerVertical="true"
                android:src="@drawable/nav_btn_back" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:text="VIP"
                android:textColor="@android:color/black"
                android:textSize="18sp"
                android:textStyle="bold" />
        </RelativeLayout>
    </androidx.appcompat.widget.Toolbar>

    <!-- 内容区整体包裹，设置背景色 -->
    <ScrollView
        android:layout_below="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true"
        android:scrollbars="none">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- 用户信息区域 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:layout_marginHorizontal="19dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <com.score.callmetest.ui.widget.CircleIconButton
                    android:id="@+id/iv_user_avatar"
                    android:layout_width="60dp"
                    android:layout_height="60dp"
                    android:background="@drawable/bg_circle_while"
                    android:src="@drawable/default_avatar" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/tv_user_name"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Deepika"
                            android:textColor="@android:color/black"
                            android:textSize="18sp"
                            android:textStyle="bold" />

                        <ImageView
                            android:id="@+id/iv_badge"
                            android:layout_width="16dp"
                            android:layout_height="16dp"
                            android:layout_marginStart="8dp"
                            android:src="@drawable/normal_badge" />
                    </LinearLayout>

                    <TextView
                        android:id="@+id/tv_vip_status"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:text="@string/you_are_not_vip"
                        android:textColor="#FF646464"
                        android:textSize="14sp" />
                </LinearLayout>
            </LinearLayout>

            <!-- VIP订阅卡片区域 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="18dp"
                android:layout_marginHorizontal="21dp"
                android:gravity="center_horizontal"
                android:orientation="horizontal">

                <!-- 1个月订阅卡片 -->
                <com.score.callmetest.ui.widget.VipItemCardView
                    android:id="@+id/vip_card_1month"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1" />

                <Space
                    android:layout_width="7dp"
                    android:layout_height="2dp" />

                <!-- 3个月订阅卡片 -->
                <com.score.callmetest.ui.widget.VipItemCardView
                    android:id="@+id/vip_card_3month"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1" />

                <Space
                    android:layout_width="7dp"
                    android:layout_height="2dp" />

                <!-- 12个月订阅卡片 -->
                <com.score.callmetest.ui.widget.VipItemCardView
                    android:id="@+id/vip_card_12month"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1" />
            </LinearLayout>

            <!-- VIP特权区域 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="19dp"
                android:background="@drawable/bg_dialog_rounded"
                android:orientation="vertical"
                android:paddingHorizontal="21dp"
                android:paddingTop="16dp">

                <!-- 特权标题 -->
                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginBottom="16dp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <ImageView
                        android:layout_width="29dp"
                        android:layout_height="22dp"
                        android:layout_marginEnd="8dp"
                        android:src="@drawable/vip_dialog_icon" />

                    <TextView
                        android:id="@+id/tv_vip_privileges_title"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Become VIP to unlock 7 privileges"
                        android:textColor="#FF5A0A0A"
                        android:textSize="15sp"
                        android:textStyle="bold" />
                </LinearLayout>

                <!-- VIP特权描述项 -->
                <com.score.callmetest.ui.widget.VipDescItemView
                    android:id="@+id/vip_desc_coins_bonus"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="7dp" />

                <com.score.callmetest.ui.widget.VipDescItemView
                    android:id="@+id/vip_desc_recharge_bonus"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:visibility="gone"
                    android:layout_marginTop="17dp" />

                <com.score.callmetest.ui.widget.VipDescItemView
                    android:id="@+id/vip_desc_call_discount"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="17dp" />

                <com.score.callmetest.ui.widget.VipDescItemView
                    android:id="@+id/vip_desc_match_call"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="17dp" />

                <com.score.callmetest.ui.widget.VipDescItemView
                    android:id="@+id/vip_desc_text_chat"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="17dp" />

                <com.score.callmetest.ui.widget.VipDescItemView
                    android:id="@+id/vip_desc_photo_album"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="17dp" />

                <com.score.callmetest.ui.widget.VipDescItemView
                    android:id="@+id/vip_desc_visitor_list"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="17dp" />

                <com.score.callmetest.ui.widget.VipDescItemView
                    android:id="@+id/vip_desc_status"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="17dp" />

                <!-- 底部间距 -->
                <Space
                    android:layout_width="match_parent"
                    android:layout_height="88dp" />
            </LinearLayout>

        </LinearLayout>
    </ScrollView>

    <View
        android:id="@+id/bottom_shadow"
        android:layout_width="match_parent"
        android:layout_height="111dp"
        android:layout_alignParentBottom="true" />

    <com.score.callmetest.ui.widget.AlphaTextView
        android:id="@+id/vip_button"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_marginHorizontal="33dp"
        android:layout_marginBottom="21dp"
        android:background="@drawable/vip_dialog_button_bg"
        android:gravity="center"
        android:lineHeight="18dp"
        android:paddingTop="15dp"
        android:paddingBottom="19dp"
        android:text="Become VIP Now"
        android:textColor="#FF9A2525"
        android:textSize="15sp"
        android:textStyle="bold" />
</RelativeLayout>