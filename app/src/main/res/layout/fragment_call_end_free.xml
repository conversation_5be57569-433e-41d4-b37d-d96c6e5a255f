<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="41dp"
        android:background="@drawable/bg_gift_bottomsheet"
        android:orientation="vertical"
        android:paddingTop="20dp"
        android:paddingBottom="15dp">

        <!-- 描述文案 -->
        <TextView
            android:id="@+id/tv_content"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="63dp"
            android:layout_marginHorizontal="70dp"
            android:gravity="center"
            android:text="1000"
            android:textColor="@color/black"
            android:fontFamily="@font/roboto_medium"
            android:textSize="18sp"
            android:textStyle="bold" />

        <com.score.callmetest.ui.widget.AlphaImageView
            android:id="@+id/close"
            android:layout_width="25dp"
            android:layout_height="25dp"
            android:layout_alignParentEnd="true"
            android:layout_marginEnd="15dp"
            android:src="@drawable/ic_close" />

        <com.score.callmetest.ui.widget.RechargeSubView
            android:id="@+id/recharge_sub_view"
            android:layout_width="match_parent"
            android:layout_marginTop="19dp"
            android:layout_height="wrap_content"
            android:layout_below="@+id/tv_content" />
    </RelativeLayout>

    <com.score.callmetest.ui.widget.CircleIconButton
        android:id="@+id/avatar"
        android:layout_width="82dp"
        android:layout_height="82dp"
        android:layout_gravity="center_horizontal" />

    <com.score.callmetest.ui.widget.FollowButton
        android:id="@+id/follow_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="69dp" />
</FrameLayout>
