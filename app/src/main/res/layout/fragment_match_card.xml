<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/constraint_layout_root"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.cardview.widget.CardView
        android:id="@+id/card_title_sum"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:cardCornerRadius="14dp"
        android:layout_marginHorizontal="17dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/constraint_layout_card_content"
            android:layout_width="match_parent"
            android:layout_height="120dp">

            <ImageView
                android:id="@+id/iv_card"
                android:layout_width="72dp"
                android:layout_height="72dp"
                android:layout_marginStart="25dp"
                android:src="@drawable/match_card_gold"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent" />

            <LinearLayout
                android:id="@+id/line_my_card"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="18dp"
                android:orientation="horizontal"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent">

                <TextView
                    android:id="@+id/tv_my_card_label"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/roboto_black"
                    android:includeFontPadding="false"
                    android:text="@string/match_card"
                    android:textColor="@color/black"
                    android:textSize="20sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tv_my_card_count"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:includeFontPadding="false"
                    android:text="3"
                    android:textColor="#FE712C"
                    android:fontFamily="@font/roboto_black"
                    android:textStyle="bold"
                    android:textSize="20sp" />
            </LinearLayout>

            <ImageView
                android:id="@+id/image_1"
                android:layout_width="92dp"
                android:layout_height="88dp"
                android:src="@drawable/bg_match_card"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintBottom_toBottomOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.cardview.widget.CardView>

    <!-- 功能列表 -->
    <androidx.cardview.widget.CardView
        android:id="@+id/card_function"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="10dp"
        android:layout_marginHorizontal="17dp"
        app:cardElevation="2dp"
        app:cardCornerRadius="14dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/card_title_sum">
        <ScrollView
            android:id="@+id/scroll_view_content"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/bg_while_rounded"
            android:fillViewport="true"
            android:scrollbars="none">

            <LinearLayout
                android:id="@+id/line_function"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <!-- What is Match Card？-->
                <TextView
                    android:id="@+id/tv_what_is_match_card_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="19dp"
                    android:layout_marginTop="20dp"
                    android:textStyle="bold"
                    android:fontFamily="@font/roboto_bold"
                    android:textColor="@color/black"
                    android:textSize="15sp"
                    android:text="@string/what_is_match_card"/>

                <TextView
                    android:id="@+id/tv_what_is_match_card_content"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="19dp"
                    android:layout_marginEnd="23dp"
                    android:fontFamily="@font/roboto_bold"
                    android:textColor="#746E6E"
                    android:layout_marginTop="12dp"
                    android:textSize="14sp"
                    android:text="@string/what_is_match_card_content"/>

                <!-- 虚线分割线 -->

                <View
                    android:id="@+id/divider_1"
                    android:layout_width="match_parent"
                    android:layout_height="2dp"
                    android:layout_marginStart="20dp"
                    android:layout_marginTop="14dp"
                    android:layout_marginBottom="20dp"
                    android:layout_marginEnd="23dp"
                    android:background="@drawable/divider_dashed"
                    android:layerType="software" />
                <!-- What's the talk time on Match Cards?-->
                <TextView
                    android:id="@+id/tv_talk_time_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="19dp"
                    android:textStyle="bold"
                    android:fontFamily="@font/roboto_bold"
                    android:textColor="@color/black"
                    android:textSize="15sp"
                    android:text="@string/what_is_talk_time"/>

                <TextView
                    android:id="@+id/tv_talk_time_content1"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="19dp"
                    android:layout_marginEnd="23dp"
                    android:fontFamily="@font/roboto_bold"
                    android:textColor="#746E6E"
                    android:layout_marginTop="12dp"
                    android:textSize="14sp"
                    android:text="@string/what_is_talk_time_content1"/>

                <TextView
                    android:id="@+id/tv_talk_time_content2"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="19dp"
                    android:layout_marginEnd="23dp"
                    android:fontFamily="@font/roboto_bold"
                    android:textColor="#746E6E"
                    android:layout_marginTop="12dp"
                    android:textSize="14sp"
                    android:text="@string/what_is_talk_time_content2"/>

                <!-- 虚线分割线 -->
                <View
                    android:id="@+id/divider_2"
                    android:layout_width="match_parent"
                    android:layout_height="2dp"
                    android:layout_marginTop="18dp"
                    android:layout_marginBottom="17dp"
                    android:layout_marginStart="20dp"
                    android:layout_marginEnd="23dp"
                    android:background="@drawable/divider_dashed"
                    android:layerType="software" />

                <!-- How to Get it? -->
                <TextView
                    android:id="@+id/tv_how_to_get_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="19dp"
                    android:textStyle="bold"
                    android:fontFamily="@font/roboto_bold"
                    android:textColor="@color/black"
                    android:textSize="15sp"
                    android:text="@string/how_to_get_it"/>

                <TextView
                    android:id="@+id/tv_how_to_get_content1"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="19dp"
                    android:layout_marginEnd="23dp"
                    android:fontFamily="@font/roboto_bold"
                    android:textColor="#746E6E"
                    android:layout_marginTop="11dp"
                    android:textSize="14sp"
                    android:text="@string/how_to_get_it_content1"/>

                <TextView
                    android:id="@+id/tv_how_to_get_content2"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="19dp"
                    android:layout_marginEnd="23dp"
                    android:fontFamily="@font/roboto_bold"
                    android:textColor="#746E6E"
                    android:layout_marginTop="10dp"
                    android:textSize="14sp"
                    android:text="@string/how_to_get_it_content2"/>

                <!-- 虚线分割线 -->
                <View
                    android:id="@+id/divider_3"
                    android:layout_width="match_parent"
                    android:layout_height="2dp"
                    android:layout_marginTop="19dp"
                    android:layout_marginBottom="14dp"
                    android:layout_marginStart="20dp"
                    android:layout_marginEnd="23dp"
                    android:background="@drawable/divider_dashed"
                    android:layerType="software" />

                <TextView
                    android:id="@+id/tv_sample_picture_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="19dp"
                    android:textStyle="bold"
                    android:fontFamily="@font/roboto_bold"
                    android:textColor="@color/black"
                    android:textSize="15sp"
                    android:text="@string/sample_picture"/>

                <ImageView
                    android:id="@+id/iv_sample_picture"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="20dp"
                    android:layout_marginTop="11dp"
                    android:scaleType="centerCrop"
                    android:src="@drawable/match_sample_picture" />

                <TextView
                    android:id="@+id/btn_match_card"
                    android:layout_width="match_parent"
                    android:layout_height="37dp"
                    android:layout_marginHorizontal="36dp"
                    android:layout_marginTop="-10dp"
                    android:textSize="13sp"
                    android:fontFamily="@font/roboto_medium"
                    android:textColor="#fff"
                    android:layout_marginBottom="25dp"
                    android:text="@string/btn_match_card_text"
                    android:background="@drawable/bg_btn_rounded_black"
                    android:gravity="center"
                    android:clickable="true"
                    android:focusable="true"/>

            </LinearLayout>

        </ScrollView>

    </androidx.cardview.widget.CardView>


</androidx.constraintlayout.widget.ConstraintLayout>