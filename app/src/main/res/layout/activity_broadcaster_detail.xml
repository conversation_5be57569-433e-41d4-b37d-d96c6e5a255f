<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root_view"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <!-- 头像和ViewPager2区域 -->
    <FrameLayout
        android:id="@+id/avatar_layout"
        android:layout_width="match_parent"
        android:layout_height="516dp">

        <ImageView
            android:id="@+id/default_avatar"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="centerCrop"
            android:transitionName="avatar" />

        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/photo_pager"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:overScrollMode="never" />

        <LinearLayout
            android:id="@+id/status_layout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom"
            android:layout_marginStart="15dp"
            android:layout_marginBottom="30dp"
            android:gravity="center"
            android:orientation="horizontal"
            android:paddingHorizontal="7dp"
            android:paddingVertical="3dp">

            <ImageView
                android:id="@+id/status_indicator"
                android:layout_width="8dp"
                android:layout_height="8dp"
                android:layout_marginEnd="3dp" />

            <TextView
                android:id="@+id/status_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@android:color/white"
                android:textSize="10sp"
                android:textStyle="bold"
                android:typeface="sans" />
        </LinearLayout>

        <TextView
            android:id="@+id/photo_count"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom|end"
            android:layout_marginEnd="15dp"
            android:layout_marginBottom="30dp"
            android:paddingHorizontal="10dp"
            android:paddingVertical="5dp"
            android:text="2/3"
            android:textColor="@android:color/white"
            android:textSize="12sp"
            android:textStyle="bold"
            android:typeface="sans"
            android:visibility="invisible" />
    </FrameLayout>


    <!-- 可拖拽的内容卡片 -->
    <RelativeLayout
        android:id="@+id/main_content_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@android:color/white"
        android:orientation="vertical"
        android:paddingStart="20dp"
        android:paddingTop="20dp"
        android:paddingEnd="20dp"
        app:layout_behavior="com.google.android.material.bottomsheet.BottomSheetBehavior">
        <!-- 为空的时候的占位图 -->
        <ViewStub
            android:id="@+id/emptyView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:inflatedId="@+id/layout_empty_bg_broadcaster_detail_parent"
            android:layout="@layout/layout_empty_bg_broadcaster_detail" />

        <androidx.core.widget.NestedScrollView
            android:id="@+id/detail_scrollview"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:fillViewport="true"
            android:overScrollMode="never">

            <RelativeLayout
                android:id="@+id/detail_content_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <com.score.callmetest.ui.widget.FollowButton
                    android:id="@+id/follow_layout"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true"
                    android:visibility="gone" />

                <LinearLayout
                    android:id="@+id/name_layout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_toStartOf="@+id/follow_layout"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/nickname"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="8dp"
                        android:ellipsize="end"
                        android:gravity="center_vertical"
                        android:includeFontPadding="false"
                        android:maxLines="1"
                        android:text="CCHUBBY🔥"
                        android:textColor="@color/black"
                        android:textSize="20sp"
                        android:textStyle="bold"
                        android:typeface="sans" />

                </LinearLayout>

                <!-- 年龄、国家、语言 -->
                <LinearLayout
                    android:id="@+id/baseinfo_layout"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/name_layout"
                    android:layout_marginTop="10dp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <!-- 年龄 -->
                    <LinearLayout
                        android:id="@+id/age_layout"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="@drawable/bg_btn_rounded_black"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:paddingLeft="8dp"
                        android:paddingTop="2dp"
                        android:paddingRight="8dp"
                        android:paddingBottom="2dp">

                        <ImageView
                            android:layout_width="12dp"
                            android:layout_height="12dp"
                            android:layout_marginEnd="4dp"
                            android:src="@drawable/age_woman"
                            android:tintMode="src_in"
                            app:tint="@color/age_color" />

                        <TextView
                            android:id="@+id/age"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:fontFamily="@font/roboto_medium"
                            android:text="26"
                            android:textColor="@color/age_color"
                            android:textSize="10sp"
                            android:textStyle="normal"
                            android:typeface="sans" />
                    </LinearLayout>

                    <!-- 国家 -->
                    <TextView
                        android:id="@+id/country"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="4dp"
                        android:background="@drawable/bg_btn_rounded_black"
                        android:fontFamily="@font/roboto_medium"
                        android:paddingLeft="8dp"
                        android:paddingTop="2dp"
                        android:paddingRight="8dp"
                        android:paddingBottom="2dp"
                        android:text="India"
                        android:textColor="@color/chat_country"
                        android:textSize="10sp"
                        android:textStyle="normal"
                        android:typeface="sans" />

                    <!-- 语言 -->
                    <LinearLayout
                        android:id="@+id/line_language"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="4dp"
                        android:background="@drawable/bg_btn_rounded_black"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:paddingLeft="8dp"
                        android:paddingTop="2dp"
                        android:paddingRight="8dp"
                        android:paddingBottom="2dp">

                        <ImageView
                            android:layout_width="14dp"
                            android:layout_height="14dp"
                            android:layout_marginEnd="4dp"
                            android:src="@drawable/chat_language" />

                        <TextView
                            android:id="@+id/tv_language"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:fontFamily="@font/roboto_medium"
                            android:text="ENGLISH"
                            android:textColor="@color/chat_lang"
                            android:textSize="10sp"
                            android:textStyle="normal"
                            android:typeface="sans" />
                    </LinearLayout>
                </LinearLayout>

                <!-- 简介 -->
                <TextView
                    android:id="@+id/intro"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/baseinfo_layout"
                    android:layout_marginTop="15dp"
                    android:text="introduce..."
                    android:textColor="#404040"
                    android:textSize="13sp" />

                <!-- 翻译结果显示区域 -->
                <TextView
                    android:id="@+id/intro_translated"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/intro"
                    android:layout_marginTop="8dp"
                    android:textColor="#FF5C00"
                    android:textSize="13sp"
                    android:visibility="gone" />

                <!-- 翻译 -->
                <com.score.callmetest.ui.widget.AlphaLinearLayout
                    android:id="@+id/translate_layout"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/intro_translated"
                    android:layout_marginTop="7dp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingVertical="3dp"
                    android:paddingEnd="12dp"
                    tools:ignore="RtlSymmetry">

                    <ImageView
                        android:id="@+id/translate_icon"
                        android:layout_width="15dp"
                        android:layout_height="15dp"
                        android:layout_marginEnd="5dp"
                        android:src="@drawable/translate" />

                    <com.opensource.svgaplayer.SVGAImageView
                        android:id="@+id/translate_svga"
                        android:layout_width="15dp"
                        android:layout_height="15dp"
                        android:layout_marginEnd="5dp"
                        android:visibility="gone" />

                    <TextView
                        android:id="@+id/translate"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Translate"
                        android:textColor="#777A84"
                        android:textSize="12sp" />
                </com.score.callmetest.ui.widget.AlphaLinearLayout>


                <LinearLayout
                    android:id="@+id/tags_layout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/translate_layout"
                    android:layout_marginTop="10dp"
                    android:orientation="horizontal">

                    <ImageView
                        android:id="@+id/tag_icon"
                        android:layout_width="22dp"
                        android:layout_height="22dp"
                        android:layout_marginTop="3dp"
                        android:layout_marginEnd="6dp"
                        android:src="@drawable/labels" />

                    <!-- 标签 -->
                    <com.google.android.flexbox.FlexboxLayout
                        android:id="@+id/tags_container"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:clipChildren="false"
                        android:clipToPadding="false"
                        app:alignItems="center"
                        app:flexWrap="wrap"
                        app:justifyContent="flex_start" />
                </LinearLayout>


                <LinearLayout
                    android:id="@+id/gift_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/tags_layout"
                    android:layout_marginTop="16dp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <!-- 礼物标题 -->
                    <FrameLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content">

                        <View
                            android:id="@+id/gift_indicator"
                            android:layout_width="27dp"
                            android:layout_height="6dp"
                            android:layout_gravity="bottom|end" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:text="Gifts Received"
                            android:textColor="#222"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            android:typeface="sans" />
                    </FrameLayout>

                    <ImageView
                        android:id="@+id/gift_arrow"
                        android:layout_width="12dp"
                        android:layout_height="12dp"
                        android:layout_marginStart="5dp"
                        android:src="@drawable/ic_black_right" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/gift_list_layout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/gift_title"
                    android:layout_marginTop="8dp"
                    android:orientation="horizontal">
                    <!-- 动态添加礼物Item -->
                </LinearLayout>

                <Space
                    android:layout_width="match_parent"
                    android:layout_height="104dp"
                    android:layout_below="@+id/gift_list_layout" />

            </RelativeLayout>
        </androidx.core.widget.NestedScrollView>
    </RelativeLayout>

    <View
        android:id="@+id/bottom_shadow"
        android:layout_width="match_parent"
        android:layout_height="136dp"
        android:layout_gravity="bottom" />

    <!-- 底部按钮区 -->
    <RelativeLayout
        android:id="@+id/line_bottom_button"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:layout_marginBottom="16dp"
        android:background="@color/transparent"
        android:gravity="bottom"
        android:orientation="horizontal"
        android:paddingHorizontal="22dp">


        <com.score.callmetest.ui.widget.AlphaImageView
            android:id="@+id/btn_chat"
            android:layout_width="86dp"
            android:layout_height="68dp"
            android:src="@drawable/info_chat" />

        <com.score.callmetest.ui.widget.AlphaLinearLayout
            android:id="@+id/video_call_layout"
            android:layout_marginStart="12dp"
            android:layout_width="match_parent"
            android:layout_toEndOf="@+id/btn_chat"
            android:layout_height="68dp"
            android:background="@drawable/video_btn_bg"
            android:layout_gravity="bottom"
            android:gravity="center"
            android:orientation="horizontal">

            <com.opensource.svgaplayer.SVGAImageView
                android:id="@+id/btn_video_call"
                android:layout_width="36dp"
                android:layout_height="36dp"
                android:backgroundTint="#E0E0E0"
                android:enabled="false" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:gravity="center"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/video_call_tip"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:gravity="center"
                        android:includeFontPadding="false"
                        android:text="Video Call"
                        android:textColor="@android:color/white"
                        android:textSize="14sp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/first_line_tip"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="3dp"
                        android:includeFontPadding="false"
                        android:textColor="@android:color/white"
                        android:textSize="12sp" />
                </LinearLayout>

                <TextView
                    android:id="@+id/second_line"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:includeFontPadding="false"
                    android:text="text"
                    android:textColor="@android:color/white"
                    android:textSize="12sp" />
            </LinearLayout>

        </com.score.callmetest.ui.widget.AlphaLinearLayout>

        <com.score.callmetest.ui.widget.AlphaFrameLayout
            android:id="@+id/vip_tip_layout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_alignParentTop="true"
            android:layout_marginEnd="12dp"
            android:elevation="4dp"
            android:layout_marginTop="-2dp"
            android:paddingHorizontal="3dp"
            android:layout_marginStart="20dp">

            <com.opensource.svgaplayer.SVGAImageView
                android:id="@+id/vip_tip_bg_svga"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:gravity="center"
                android:paddingHorizontal="7dp"
                android:paddingTop="3dp"
                android:paddingBottom="5dp"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="15dp"
                    android:layout_height="15dp"
                    android:layout_marginEnd="2dp"
                    android:src="@drawable/vip_dialog_icon" />

                <TextView
                    android:id="@+id/vip_discount_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:lineHeight="11dp"
                    android:textColor="@android:color/white"
                    android:textSize="10sp" />
            </LinearLayout>
        </com.score.callmetest.ui.widget.AlphaFrameLayout>
    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/top_layout"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:paddingHorizontal="15dp">

        <com.score.callmetest.ui.widget.AlphaImageView
            android:id="@+id/back"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_centerVertical="true"
            android:src="@drawable/info_btn_back" />

        <com.score.callmetest.ui.widget.AlphaImageView
            android:id="@+id/more"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:src="@drawable/info_more" />
    </RelativeLayout>

</androidx.coordinatorlayout.widget.CoordinatorLayout>